import 'package:sqflite/sqflite.dart';
import '../../../../core/database/database_service.dart';
import '../models/purchase_model.dart';
import '../models/purchase_item_model.dart';

class PurchaseDatabaseService {
  final DatabaseService _databaseService;

  PurchaseDatabaseService(this._databaseService);

  /// Create a new purchase with its items
  Future<int> createPurchase(PurchaseModel purchase, List<PurchaseItemModel> items) async {
    try {
      final db = await _databaseService.database;
      
      return await db.transaction((txn) async {
        // Insert purchase
        final purchaseId = await txn.insert(
          'purchases',
          purchase.toMap()..remove('id'), // Remove id for auto-increment
          conflictAlgorithm: ConflictAlgorithm.replace,
        );

        // Insert purchase items
        for (final item in items) {
          await txn.insert(
            'purchase_items',
            item.copyWith(purchaseId: purchaseId).toMap()..remove('id'),
            conflictAlgorithm: ConflictAlgorithm.replace,
          );
        }

        return purchaseId;
      });
    } catch (e) {
      throw Exception('Failed to create purchase: $e');
    }
  }

  /// Get all purchases
  Future<List<PurchaseModel>> getAllPurchases() async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'purchases',
        orderBy: 'purchaseDate DESC, id DESC', // Most recent first
      );

      return List.generate(maps.length, (i) {
        return PurchaseModel.fromMap(maps[i]);
      });
    } catch (e) {
      throw Exception('Failed to get purchases: $e');
    }
  }

  /// Get purchase by ID
  Future<PurchaseModel?> getPurchaseById(int id) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'purchases',
        where: 'id = ?',
        whereArgs: [id],
        limit: 1,
      );

      if (maps.isNotEmpty) {
        return PurchaseModel.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get purchase by id: $e');
    }
  }

  /// Get purchase items by purchase ID
  Future<List<PurchaseItemModel>> getPurchaseItems(int purchaseId) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'purchase_items',
        where: 'purchaseId = ?',
        whereArgs: [purchaseId],
        orderBy: 'id ASC',
      );

      return List.generate(maps.length, (i) {
        return PurchaseItemModel.fromMap(maps[i]);
      });
    } catch (e) {
      throw Exception('Failed to get purchase items: $e');
    }
  }

  /// Update purchase
  Future<void> updatePurchase(PurchaseModel purchase) async {
    try {
      final db = await _databaseService.database;
      await db.update(
        'purchases',
        purchase.toMap(),
        where: 'id = ?',
        whereArgs: [purchase.id],
      );
    } catch (e) {
      throw Exception('Failed to update purchase: $e');
    }
  }

  /// Update purchase with items
  Future<void> updatePurchaseWithItems(
    PurchaseModel purchase,
    List<PurchaseItemModel> items,
  ) async {
    try {
      final db = await _databaseService.database;
      
      await db.transaction((txn) async {
        // Update purchase
        await txn.update(
          'purchases',
          purchase.toMap(),
          where: 'id = ?',
          whereArgs: [purchase.id],
        );

        // Delete existing items
        await txn.delete(
          'purchase_items',
          where: 'purchaseId = ?',
          whereArgs: [purchase.id],
        );

        // Insert new items
        for (final item in items) {
          await txn.insert(
            'purchase_items',
            item.copyWith(purchaseId: purchase.id!).toMap()..remove('id'),
            conflictAlgorithm: ConflictAlgorithm.replace,
          );
        }
      });
    } catch (e) {
      throw Exception('Failed to update purchase with items: $e');
    }
  }

  /// Delete purchase
  Future<void> deletePurchase(int id) async {
    try {
      final db = await _databaseService.database;
      await db.delete(
        'purchases',
        where: 'id = ?',
        whereArgs: [id],
      );
      // Purchase items will be deleted automatically due to CASCADE
    } catch (e) {
      throw Exception('Failed to delete purchase: $e');
    }
  }

  /// Get purchases by supplier
  Future<List<PurchaseModel>> getPurchasesBySupplier(int supplierId) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'purchases',
        where: 'supplierId = ?',
        whereArgs: [supplierId],
        orderBy: 'purchaseDate DESC, id DESC',
      );

      return List.generate(maps.length, (i) {
        return PurchaseModel.fromMap(maps[i]);
      });
    } catch (e) {
      throw Exception('Failed to get purchases by supplier: $e');
    }
  }

  /// Get purchases by status
  Future<List<PurchaseModel>> getPurchasesByStatus(String status) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'purchases',
        where: 'status = ?',
        whereArgs: [status],
        orderBy: 'purchaseDate DESC, id DESC',
      );

      return List.generate(maps.length, (i) {
        return PurchaseModel.fromMap(maps[i]);
      });
    } catch (e) {
      throw Exception('Failed to get purchases by status: $e');
    }
  }

  /// Get purchases by payment method
  Future<List<PurchaseModel>> getPurchasesByPaymentMethod(String paymentMethod) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'purchases',
        where: 'paymentMethod = ?',
        whereArgs: [paymentMethod],
        orderBy: 'purchaseDate DESC, id DESC',
      );

      return List.generate(maps.length, (i) {
        return PurchaseModel.fromMap(maps[i]);
      });
    } catch (e) {
      throw Exception('Failed to get purchases by payment method: $e');
    }
  }

  /// Get purchase statistics
  Future<Map<String, dynamic>> getPurchaseStatistics() async {
    try {
      final db = await _databaseService.database;
      
      // Get total purchases count
      final countResult = await db.rawQuery(
        'SELECT COUNT(*) as count FROM purchases'
      );
      final totalCount = countResult.first['count'] as int;

      // Get total purchase amount
      final amountResult = await db.rawQuery(
        'SELECT SUM(totalAmount) as totalAmount, SUM(paidAmount) as totalPaid, SUM(dueAmount) as totalDue FROM purchases'
      );
      final totalAmount = (amountResult.first['totalAmount'] as num?)?.toDouble() ?? 0.0;
      final totalPaid = (amountResult.first['totalPaid'] as num?)?.toDouble() ?? 0.0;
      final totalDue = (amountResult.first['totalDue'] as num?)?.toDouble() ?? 0.0;

      // Get counts by status
      final statusResult = await db.rawQuery('''
        SELECT status, COUNT(*) as count 
        FROM purchases 
        GROUP BY status
      ''');

      final statusCounts = <String, int>{};
      for (final row in statusResult) {
        statusCounts[row['status'] as String] = row['count'] as int;
      }

      // Get counts by payment method
      final paymentResult = await db.rawQuery('''
        SELECT paymentMethod, COUNT(*) as count 
        FROM purchases 
        GROUP BY paymentMethod
      ''');

      final paymentCounts = <String, int>{};
      for (final row in paymentResult) {
        paymentCounts[row['paymentMethod'] as String] = row['count'] as int;
      }

      return {
        'totalCount': totalCount,
        'totalAmount': totalAmount,
        'totalPaid': totalPaid,
        'totalDue': totalDue,
        'statusCounts': statusCounts,
        'paymentCounts': paymentCounts,
      };
    } catch (e) {
      throw Exception('Failed to get purchase statistics: $e');
    }
  }
}
