import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:get_it/get_it.dart';
import 'package:market/shared_widgets/wrappers.dart';
import '../providers/supplier_provider.dart';
import '../../domain/entities/supplier.dart';
import '../../../purchases/domain/repositories/purchase_repository.dart';

class SupplierStatementsScreen extends StatefulWidget {
  const SupplierStatementsScreen({super.key});

  @override
  State<SupplierStatementsScreen> createState() =>
      _SupplierStatementsScreenState();
}

class _SupplierStatementsScreenState extends State<SupplierStatementsScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  final Map<int, double> _supplierTotals = {};

  @override
  void initState() {
    super.initState();
    // Load suppliers when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<SupplierProvider>().loadSuppliers();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<double> _getSupplierTotal(int supplierId) async {
    if (_supplierTotals.containsKey(supplierId)) {
      return _supplierTotals[supplierId]!;
    }

    try {
      final purchaseRepository = GetIt.instance<PurchaseRepository>();
      final total = await purchaseRepository.getTotalPurchasesBySupplier(
        supplierId,
      );
      _supplierTotals[supplierId] = total;
      return total;
    } catch (e) {
      return 0.0;
    }
  }

  @override
  Widget build(BuildContext context) {
    return SecondaryScreenWrapper(
      title: 'كشوفات الموردين',
      child: Consumer<SupplierProvider>(
        builder: (context, supplierProvider, child) {
          if (supplierProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (supplierProvider.errorMessage != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
                  const SizedBox(height: 16),
                  Text(
                    'خطأ في تحميل البيانات',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    supplierProvider.errorMessage!,
                    style: Theme.of(context).textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => supplierProvider.loadSuppliers(),
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            );
          }

          if (supplierProvider.suppliers.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.local_shipping_outlined,
                    size: 64,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'لا يوجد موردين',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'لا توجد كشوفات حسابات للعرض',
                    style: Theme.of(context).textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            );
          }

          return Column(
            children: [
              // Search bar
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'البحث في الموردين...',
                    prefixIcon: const Icon(Icons.search),
                    border: const OutlineInputBorder(),
                    suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                              setState(() {
                                _searchQuery = '';
                              });
                            },
                          )
                        : null,
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
              ),
              // Suppliers list
              Expanded(
                child: Builder(
                  builder: (context) {
                    final filteredSuppliers = _searchQuery.isEmpty
                        ? supplierProvider.suppliers
                        : supplierProvider.searchSuppliers(_searchQuery);

                    if (filteredSuppliers.isEmpty && _searchQuery.isNotEmpty) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.search_off,
                              size: 64,
                              color: Colors.grey[400],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'لا توجد نتائج للبحث',
                              style: Theme.of(context).textTheme.headlineSmall,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'جرب البحث بكلمات مختلفة',
                              style: Theme.of(context).textTheme.bodyMedium,
                            ),
                          ],
                        ),
                      );
                    }

                    return RefreshIndicator(
                      onRefresh: () async {
                        await supplierProvider.loadSuppliers();
                        _supplierTotals.clear(); // Clear cached totals
                      },
                      child: ListView.builder(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        itemCount: filteredSuppliers.length,
                        itemBuilder: (context, index) {
                          final supplier = filteredSuppliers[index];
                          return _buildSupplierStatementCard(context, supplier);
                        },
                      ),
                    );
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSupplierStatementCard(BuildContext context, Supplier supplier) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: Theme.of(context).colorScheme.secondary,
          child: Text(
            supplier.name.isNotEmpty ? supplier.name[0].toUpperCase() : 'م',
            style: TextStyle(
              color: Theme.of(context).colorScheme.onSecondary,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(
          supplier.name,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: FutureBuilder<double>(
          future: _getSupplierTotal(supplier.id!),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Text('جاري التحميل...');
            }

            if (snapshot.hasError) {
              return const Text('خطأ في تحميل البيانات');
            }

            final total = snapshot.data ?? 0.0;
            return Text(
              'إجمالي المشتريات: ${total.toStringAsFixed(2)} ر.ي',
              style: TextStyle(
                color: total > 0 ? Colors.blue : Colors.grey,
                fontWeight: FontWeight.w600,
              ),
            );
          },
        ),
        trailing: const Icon(Icons.arrow_forward_ios),
        onTap: () {
          context.go('/suppliers/view/${supplier.id}');
        },
      ),
    );
  }
}
