import '../../data/models/customer_account_model.dart';

class CustomerAccount {
  final int? id;
  final int customerId;
  final DateTime transactionDate;
  final String type; // 'sale_invoice', 'retail_debt', 'payment', 'return'
  final double amount;
  final String? description;
  final int? relatedInvoiceId;
  final bool isPaid;

  const CustomerAccount({
    this.id,
    required this.customerId,
    required this.transactionDate,
    required this.type,
    required this.amount,
    this.description,
    this.relatedInvoiceId,
    this.isPaid = false,
  });

  // Create CustomerAccount from CustomerAccountModel
  factory CustomerAccount.fromModel(CustomerAccountModel model) {
    return CustomerAccount(
      id: model.id,
      customerId: model.customerId,
      transactionDate: model.transactionDate,
      type: model.type,
      amount: model.amount,
      description: model.description,
      relatedInvoiceId: model.relatedInvoiceId,
      isPaid: model.isPaid,
    );
  }

  // Business logic methods
  bool get isDebit => type == 'sale_invoice' || type == 'retail_debt';
  bool get isCredit => type == 'payment' || type == 'return';
  
  String get typeDisplayName {
    switch (type) {
      case 'sale_invoice':
        return 'فاتورة بيع';
      case 'retail_debt':
        return 'دين تجزئة';
      case 'payment':
        return 'دفعة';
      case 'return':
        return 'مرتجع';
      default:
        return type;
    }
  }

  String get formattedAmount => '${amount.toStringAsFixed(2)} ج.م';
  
  String get statusText => isPaid ? 'مدفوع' : 'غير مدفوع';

  // Copy with method for updates
  CustomerAccount copyWith({
    int? id,
    int? customerId,
    DateTime? transactionDate,
    String? type,
    double? amount,
    String? description,
    int? relatedInvoiceId,
    bool? isPaid,
  }) {
    return CustomerAccount(
      id: id ?? this.id,
      customerId: customerId ?? this.customerId,
      transactionDate: transactionDate ?? this.transactionDate,
      type: type ?? this.type,
      amount: amount ?? this.amount,
      description: description ?? this.description,
      relatedInvoiceId: relatedInvoiceId ?? this.relatedInvoiceId,
      isPaid: isPaid ?? this.isPaid,
    );
  }

  @override
  String toString() {
    return 'CustomerAccount(id: $id, customerId: $customerId, '
        'transactionDate: $transactionDate, type: $type, amount: $amount, '
        'description: $description, relatedInvoiceId: $relatedInvoiceId, '
        'isPaid: $isPaid)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CustomerAccount &&
        other.id == id &&
        other.customerId == customerId &&
        other.transactionDate == transactionDate &&
        other.type == type &&
        other.amount == amount &&
        other.description == description &&
        other.relatedInvoiceId == relatedInvoiceId &&
        other.isPaid == isPaid;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        customerId.hashCode ^
        transactionDate.hashCode ^
        type.hashCode ^
        amount.hashCode ^
        description.hashCode ^
        relatedInvoiceId.hashCode ^
        isPaid.hashCode;
  }
}
