import '../../domain/entities/customer.dart';
import '../../domain/repositories/customer_repository.dart';
import '../datasources/customer_database_service.dart';
import '../models/customer_model.dart';

class CustomerRepositoryImpl implements CustomerRepository {
  final CustomerDatabaseService _databaseService;

  CustomerRepositoryImpl(this._databaseService);

  @override
  Future<List<Customer>> getAllCustomers() async {
    try {
      final customerModels = await _databaseService.getAllCustomers();
      return customerModels.map((model) => _modelToEntity(model)).toList();
    } catch (e) {
      throw Exception('Failed to get customers: $e');
    }
  }

  @override
  Future<Customer?> getCustomerById(int id) async {
    try {
      final customerModel = await _databaseService.getCustomerById(id);
      if (customerModel != null) {
        return _modelToEntity(customerModel);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get customer by id: $e');
    }
  }

  @override
  Future<int> createCustomer(Customer customer) async {
    try {
      final customerModel = _entityToModel(customer);
      return await _databaseService.createCustomer(customerModel);
    } catch (e) {
      throw Exception('Failed to create customer: $e');
    }
  }

  @override
  Future<void> updateCustomer(Customer customer) async {
    try {
      final customerModel = _entityToModel(customer);
      await _databaseService.updateCustomer(customerModel);
    } catch (e) {
      throw Exception('Failed to update customer: $e');
    }
  }

  @override
  Future<void> deleteCustomer(int id) async {
    try {
      await _databaseService.deleteCustomer(id);
    } catch (e) {
      throw Exception('Failed to delete customer: $e');
    }
  }

  @override
  Future<List<Customer>> searchCustomers(String query) async {
    try {
      final customerModels = await _databaseService.searchCustomers(query);
      return customerModels.map((model) => _modelToEntity(model)).toList();
    } catch (e) {
      throw Exception('Failed to search customers: $e');
    }
  }

  @override
  Future<void> addAccountEntry(
    int customerId,
    double amount,
    String type,
    String description,
  ) async {
    try {
      return await _databaseService.addAccountEntry(
        customerId,
        amount,
        type,
        description,
      );
    } catch (e) {
      throw Exception('Failed to add account entry: $e');
    }
  }

  @override
  Future<List<Map<String, dynamic>>> getAccountStatement(int customerId) async {
    try {
      return await _databaseService.getAccountStatement(customerId);
    } catch (e) {
      throw Exception('Failed to get account statement: $e');
    }
  }

  Customer _modelToEntity(CustomerModel model) {
    return Customer(
      id: model.id,
      name: model.name,
      phone: model.phone,
      email: model.email,
      address: model.address,
      creditLimit: model.creditLimit,
      currentBalance: model.currentBalance,
      notes: model.notes,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
    );
  }

  CustomerModel _entityToModel(Customer entity) {
    return CustomerModel(
      id: entity.id,
      name: entity.name,
      phone: entity.phone,
      email: entity.email,
      address: entity.address,
      creditLimit: entity.creditLimit,
      currentBalance: entity.currentBalance,
      notes: entity.notes,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    );
  }
}
