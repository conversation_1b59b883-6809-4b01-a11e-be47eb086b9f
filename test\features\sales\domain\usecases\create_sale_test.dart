import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:market/features/sales/domain/entities/sale.dart';
import 'package:market/features/sales/domain/entities/sale_item.dart';
import 'package:market/features/sales/domain/repositories/sale_repository.dart';
import 'package:market/features/sales/domain/usecases/create_sale.dart';

import 'create_sale_test.mocks.dart';

@GenerateMocks([SaleRepository])
void main() {
  late CreateSaleUseCase useCase;
  late MockSaleRepository mockSaleRepository;

  setUp(() {
    mockSaleRepository = MockSaleRepository();
    useCase = CreateSaleUseCase(mockSaleRepository);
  });

  group('CreateSaleUseCase', () {
    final testSaleItems = [
      const SaleItem(
        saleId: 1,
        productId: 1,
        quantity: 2,
        unitPrice: 100.0,
        itemType: 'wholesale_product',
        description: 'منتج تجريبي 1',
      ),
      const SaleItem(
        saleId: 1,
        unitPrice: 150.0,
        itemType: 'retail_goods_summary',
        description: 'بضاعة تجزئة متنوعة',
      ),
    ];

    final testSale = Sale(
      customerId: 1,
      saleDate: DateTime.now(),
      totalAmount: 350.0,
      paidAmount: 300.0,
      dueAmount: 50.0,
      paymentMethod: 'cash',
      status: 'completed',
      notes: 'ملاحظات تجريبية',
    );

    test('يجب أن ينجح في إنشاء فاتورة صحيحة', () async {
      // Arrange
      when(mockSaleRepository.createSale(any, any))
          .thenAnswer((_) async => 1);

      // Act
      final result = await useCase.call(testSale, testSaleItems);

      // Assert
      expect(result, 1);
      verify(mockSaleRepository.createSale(testSale, testSaleItems));
      verifyNoMoreInteractions(mockSaleRepository);
    });

    test('يجب أن يفشل عند إدخال فاتورة بقائمة عناصر فارغة', () async {
      // Act & Assert
      expect(
        () async => await useCase.call(testSale, []),
        throwsA(isA<Exception>()),
      );
      verifyNever(mockSaleRepository.createSale(any, any));
    });

    test('يجب أن يفشل عند إدخال فاتورة بإجمالي سالب', () async {
      // Arrange
      final invalidSale = testSale.copyWith(totalAmount: -100.0);

      // Act & Assert
      expect(
        () async => await useCase.call(invalidSale, testSaleItems),
        throwsA(isA<Exception>()),
      );
      verifyNever(mockSaleRepository.createSale(any, any));
    });

    test('يجب أن يفشل عند إدخال فاتورة بمبلغ مدفوع سالب', () async {
      // Arrange
      final invalidSale = testSale.copyWith(paidAmount: -50.0);

      // Act & Assert
      expect(
        () async => await useCase.call(invalidSale, testSaleItems),
        throwsA(isA<Exception>()),
      );
      verifyNever(mockSaleRepository.createSale(any, any));
    });

    test('يجب أن يرمي استثناء عند فشل المستودع', () async {
      // Arrange
      when(mockSaleRepository.createSale(any, any))
          .thenThrow(Exception('فشل في قاعدة البيانات'));

      // Act & Assert
      expect(
        () async => await useCase.call(testSale, testSaleItems),
        throwsA(isA<Exception>()),
      );
      verify(mockSaleRepository.createSale(testSale, testSaleItems));
    });
  });
}
