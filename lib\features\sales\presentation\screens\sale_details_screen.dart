import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:get_it/get_it.dart';
import 'package:market/shared_widgets/wrappers.dart';
import '../providers/sale_provider.dart';
import '../../domain/entities/sale.dart';
import '../../domain/entities/sale_item.dart';
import '../../../products/domain/usecases/get_product_by_id.dart';
import '../../../customers/presentation/providers/customer_provider.dart';
import '../../../customers/domain/entities/customer.dart';
import 'package:intl/intl.dart';

class SaleDetailsScreen extends StatefulWidget {
  final int saleId;

  const SaleDetailsScreen({super.key, required this.saleId});

  @override
  State<SaleDetailsScreen> createState() => _SaleDetailsScreenState();
}

class _SaleDetailsScreenState extends State<SaleDetailsScreen> {
  Sale? sale;
  List<SaleItem> saleItems = [];
  Customer? customer;
  bool isLoading = true;
  String? errorMessage;

  @override
  void initState() {
    super.initState();
    _loadSale();
  }

  Future<void> _loadSale() async {
    try {
      setState(() {
        isLoading = true;
        errorMessage = null;
      });

      final saleProvider = context.read<SaleProvider>();
      final customerProvider = context.read<CustomerProvider>();

      // تحميل الفاتورة
      final loadedSale = await saleProvider.getSaleById(widget.saleId);
      if (loadedSale == null) {
        throw Exception('لم يتم العثور على الفاتورة');
      }

      // تحميل عناصر الفاتورة
      final loadedSaleItems = await saleProvider.getSaleItems(widget.saleId);

      // تحميل بيانات العميل إذا كان موجوداً
      Customer? loadedCustomer;
      if (loadedSale.customerId != null) {
        loadedCustomer = await customerProvider.getCustomerById(
          loadedSale.customerId!,
        );
      }

      if (mounted) {
        setState(() {
          sale = loadedSale;
          saleItems = loadedSaleItems;
          customer = loadedCustomer;
          isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          errorMessage = 'خطأ في تحميل بيانات الفاتورة: ${e.toString()}';
          isLoading = false;
        });
      }
    }
  }

  // دوال وهمية للطباعة والمشاركة
  void _printInvoice() {
    // TODO: تنفيذ وظيفة الطباعة الفعلية
    debugPrint('طباعة فاتورة رقم ${widget.saleId}');
  }

  void _shareInvoice() {
    // TODO: تنفيذ وظيفة المشاركة الفعلية
    debugPrint('مشاركة فاتورة رقم ${widget.saleId}');
  }

  Future<void> _deleteSale() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text(
          'هل أنت متأكد من حذف هذه الفاتورة؟ لا يمكن التراجع عن هذا الإجراء.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      try {
        await context.read<SaleProvider>().deleteSale(widget.saleId);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم حذف الفاتورة بنجاح')),
          );
          context.pop();
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('خطأ في حذف الفاتورة: ${e.toString()}')),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return SecondaryScreenWrapper(
      title: sale != null ? 'فاتورة رقم ${sale!.id}' : 'تفاصيل الفاتورة',
      actions: [
        if (sale != null) ...[
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4),
            child: IconButton(
              icon: const Icon(Icons.print),
              onPressed: _printInvoice,
              tooltip: 'طباعة',
              style: IconButton.styleFrom(
                backgroundColor: Colors.blue.shade50,
                foregroundColor: Colors.blue.shade700,
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4),
            child: IconButton(
              icon: const Icon(Icons.share),
              onPressed: _shareInvoice,
              tooltip: 'مشاركة',
              style: IconButton.styleFrom(
                backgroundColor: Colors.green.shade50,
                foregroundColor: Colors.green.shade700,
              ),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () => context.go('/sales/edit/${widget.saleId}'),
            tooltip: 'تعديل',
          ),
          IconButton(
            icon: const Icon(Icons.delete),
            onPressed: _deleteSale,
            tooltip: 'حذف',
          ),
        ],
      ],
      child: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'خطأ في تحميل البيانات',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              errorMessage!,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadSale,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (sale == null) {
      return const Center(child: Text('لم يتم العثور على الفاتورة'));
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSaleHeaderCard(),
          const SizedBox(height: 16),
          _buildSaleItemsCard(),
          const SizedBox(height: 16),
          _buildCustomerInfoCard(),
          const SizedBox(height: 16),
          _buildPaymentInfoCard(),
          const SizedBox(height: 16),
          _buildStatusInfoCard(),
          const SizedBox(height: 16),
          _buildAdditionalInfoCard(),
        ],
      ),
    );
  }

  Widget _buildSaleHeaderCard() {
    final dateFormat = DateFormat('dd/MM/yyyy HH:mm');

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // ترويسة الفاتورة
            Center(
              child: Column(
                children: [
                  Text(
                    'أُسامة ماركت',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.blue.shade800,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'محطة المسعودي - الشارع العام',
                    style: Theme.of(
                      context,
                    ).textTheme.bodySmall?.copyWith(color: Colors.grey[700]),
                  ),
                  Text(
                    'رقم الهاتف: 739740717',
                    style: Theme.of(
                      context,
                    ).textTheme.bodySmall?.copyWith(color: Colors.grey[700]),
                  ),
                ],
              ),
            ),
            const Divider(height: 20, thickness: 1),

            // عنوان الفاتورة ورقمها
            Center(
              child: Text(
                'فاتورة مبيعات رقم ${sale!.id}',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                ),
              ),
            ),
            const SizedBox(height: 16),

            // معلومات الفاتورة
            Row(
              children: [
                const CircleAvatar(
                  radius: 30,
                  backgroundColor: Colors.green,
                  child: Icon(
                    Icons.shopping_cart,
                    color: Colors.white,
                    size: 30,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'التاريخ: ${dateFormat.format(sale!.saleDate)}',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'العميل: ${customer?.name ?? 'عميل عابر'}',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '${sale!.totalAmount.toStringAsFixed(2)} ر.س',
                      style: Theme.of(context).textTheme.headlineSmall
                          ?.copyWith(
                            color: Colors.green,
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSaleItemsCard() {
    if (saleItems.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Icon(
                Icons.shopping_cart_outlined,
                size: 48,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 8),
              Text(
                'لا توجد عناصر في هذه الفاتورة',
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
              ),
            ],
          ),
        ),
      );
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'عناصر الفاتورة',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            ...saleItems.asMap().entries.map((entry) {
              final index = entry.key;
              final item = entry.value;
              return _buildSaleItemRow(item, index);
            }),
            const Divider(),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'الإجمالي:',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '${sale!.totalAmount.toStringAsFixed(2)} ر.س',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSaleItemRow(SaleItem item, int index) {
    return FutureBuilder<String>(
      future: _getProductName(item),
      builder: (context, snapshot) {
        final productName = snapshot.data ?? 'جاري التحميل...';

        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Row(
            children: [
              Container(
                width: 30,
                height: 30,
                decoration: BoxDecoration(
                  color: Colors.blue.shade100,
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Center(
                  child: Text(
                    '${index + 1}',
                    style: TextStyle(
                      color: Colors.blue.shade700,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      productName,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    if (item.description != null &&
                        item.description!.isNotEmpty)
                      Text(
                        item.description!,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    Text(
                      '${item.quantity ?? 1} × ${item.unitPrice.toStringAsFixed(2)} ر.س',
                      style: Theme.of(
                        context,
                      ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                    ),
                  ],
                ),
              ),
              Text(
                '${item.totalPrice.toStringAsFixed(2)} ر.س',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Future<String> _getProductName(SaleItem item) async {
    if (item.isRetailGoodsSummary) {
      return item.description ?? 'بند بقالة';
    }

    if (item.isWholesaleProduct && item.productId != null) {
      try {
        final getProductByIdUseCase = GetIt.instance<GetProductByIdUseCase>();
        final product = await getProductByIdUseCase.call(item.productId!);
        return product?.name ?? 'منتج رقم ${item.productId}';
      } catch (e) {
        return 'منتج رقم ${item.productId}';
      }
    }

    return item.description ?? 'بند غير محدد';
  }

  Widget _buildCustomerInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات العميل',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            _buildInfoRow(
              Icons.person,
              'العميل',
              customer?.name ?? 'عميل عابر',
            ),
            if (customer?.phone != null && customer!.phone!.isNotEmpty)
              _buildInfoRow(Icons.phone, 'رقم الهاتف', customer!.phone!),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات الدفع',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            _buildInfoRow(
              Icons.payment,
              'طريقة الدفع',
              sale!.paymentMethodDisplayName,
            ),
            _buildInfoRow(
              Icons.account_balance_wallet,
              'حالة الدفع',
              sale!.paymentStatusText,
              valueColor: sale!.paymentStatus == 'paid'
                  ? Colors.green
                  : Colors.orange,
            ),
            _buildInfoRow(
              Icons.money,
              'المبلغ الإجمالي',
              '${sale!.totalAmount.toStringAsFixed(2)} ر.س',
              valueColor: Colors.green,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'حالة الفاتورة',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            _buildInfoRow(
              Icons.info,
              'الحالة',
              sale!.statusDisplayName,
              valueColor: sale!.status == 'completed'
                  ? Colors.green
                  : Colors.orange,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdditionalInfoCard() {
    final dateFormat = DateFormat('dd/MM/yyyy HH:mm');

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات إضافية',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            if (sale!.createdAt != null)
              _buildInfoRow(
                Icons.calendar_today,
                'تاريخ الإنشاء',
                dateFormat.format(sale!.createdAt!),
              ),
            if (sale!.updatedAt != null)
              _buildInfoRow(
                Icons.update,
                'آخر تحديث',
                dateFormat.format(sale!.updatedAt!),
              ),
            if (sale!.notes != null && sale!.notes!.isNotEmpty)
              _buildInfoRow(Icons.note, 'ملاحظات', sale!.notes!),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(
    IconData icon,
    String label,
    String value, {
    Color? valueColor,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: Theme.of(
                    context,
                  ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: valueColor,
                    fontWeight: valueColor != null ? FontWeight.bold : null,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
