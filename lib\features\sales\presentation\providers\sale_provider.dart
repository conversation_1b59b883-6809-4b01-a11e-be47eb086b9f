import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import '../../domain/entities/sale.dart';
import '../../domain/entities/sale_item.dart';
import '../../domain/usecases/create_sale.dart';
import '../../domain/usecases/get_all_sales.dart';
import '../../domain/usecases/get_sale_by_id.dart';
import '../../domain/usecases/update_sale.dart';
import '../../domain/usecases/delete_sale.dart';
import '../../domain/repositories/sale_repository.dart';
import '../../../products/presentation/providers/product_provider.dart';
import '../../../customers/presentation/providers/customer_provider.dart';
import '../../../activities/presentation/providers/activity_provider.dart';

class SaleProvider extends ChangeNotifier {
  final CreateSaleUseCase _createSaleUseCase;
  final GetAllSalesUseCase _getAllSalesUseCase;
  final GetSaleByIdUseCase _getSaleByIdUseCase;
  final UpdateSaleUseCase _updateSaleUseCase;
  final DeleteSaleUseCase _deleteSaleUseCase;
  final SaleRepository _saleRepository;

  SaleProvider()
    : _createSaleUseCase = GetIt.instance<CreateSaleUseCase>(),
      _getAllSalesUseCase = GetIt.instance<GetAllSalesUseCase>(),
      _getSaleByIdUseCase = GetIt.instance<GetSaleByIdUseCase>(),
      _updateSaleUseCase = GetIt.instance<UpdateSaleUseCase>(),
      _deleteSaleUseCase = GetIt.instance<DeleteSaleUseCase>(),
      _saleRepository = GetIt.instance<SaleRepository>();

  // State variables
  List<Sale> _sales = [];
  bool _isLoading = false;
  String? _errorMessage;

  // Getters
  List<Sale> get sales => _sales;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  // Fetch all sales
  Future<void> fetchSales() async {
    _setLoading(true);
    _clearError();

    try {
      _sales = await _getAllSalesUseCase.call();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load sales: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Create sale with atomic transaction
  Future<bool> createSale(Sale sale, List<SaleItem> items) async {
    _setLoading(true);
    _clearError();

    try {
      // Get required providers
      final productProvider = GetIt.instance<ProductProvider>();
      final customerProvider = GetIt.instance<CustomerProvider>();
      final activityProvider = GetIt.instance<ActivityProvider>();

      // Create sale using use case (this handles the database transaction)
      final saleId = await _createSaleUseCase.call(sale, items);

      // Update product quantities and batches for wholesale products
      for (final item in items) {
        if (item.isWholesaleProduct &&
            item.productId != null &&
            item.quantity != null) {
          await productProvider.updateProductQuantitiesForSalesAndPurchases(
            item.productId!,
            item.quantity!,
            isDecrease: true, // Decrease for sale
          );
        }
      }

      // Add customer account entry if credit sale
      if (sale.customerId != null &&
          sale.paymentMethod == 'credit' &&
          sale.dueAmount > 0) {
        await customerProvider.addSaleToAccount(
          sale.customerId!,
          saleId,
          sale.dueAmount,
          'فاتورة بيع رقم $saleId',
        );
      }

      // Refresh sales list
      await fetchSales();

      // Refresh activities
      await activityProvider.refreshActivities();

      return true;
    } catch (e) {
      _setError('Failed to create sale: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Update sale
  Future<bool> updateSale(Sale sale) async {
    _setLoading(true);
    _clearError();

    try {
      await _updateSaleUseCase.call(sale);
      await fetchSales(); // Refresh list
      return true;
    } catch (e) {
      _setError('Failed to update sale: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Update sale with items
  Future<bool> updateSaleWithItems(Sale sale, List<SaleItem> items) async {
    _setLoading(true);
    _clearError();

    try {
      await _saleRepository.updateSaleWithItems(sale, items);
      await fetchSales(); // Refresh list
      return true;
    } catch (e) {
      _setError('Failed to update sale with items: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Delete sale
  Future<bool> deleteSale(int id) async {
    _setLoading(true);
    _clearError();

    try {
      await _deleteSaleUseCase.call(id);
      await fetchSales(); // Refresh list
      return true;
    } catch (e) {
      _setError('Failed to delete sale: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Get sale by ID
  Future<Sale?> getSaleById(int id) async {
    try {
      return await _getSaleByIdUseCase.call(id);
    } catch (e) {
      _setError('Failed to get sale: ${e.toString()}');
      return null;
    }
  }

  // Get sale items
  Future<List<SaleItem>> getSaleItems(int saleId) async {
    try {
      return await _saleRepository.getSaleItems(saleId);
    } catch (e) {
      _setError('Failed to get sale items: ${e.toString()}');
      return [];
    }
  }

  // Get sales by customer
  Future<List<Sale>> getSalesByCustomer(int customerId) async {
    try {
      return await _saleRepository.getSalesByCustomer(customerId);
    } catch (e) {
      _setError('Failed to get sales by customer: ${e.toString()}');
      return [];
    }
  }

  // Get sales by status
  List<Sale> getSalesByStatus(String status) {
    return _sales.where((sale) => sale.status == status).toList();
  }

  // Get sales by payment method
  List<Sale> getSalesByPaymentMethod(String paymentMethod) {
    return _sales.where((sale) => sale.paymentMethod == paymentMethod).toList();
  }

  // Get sales statistics
  Future<Map<String, dynamic>> getSalesStatistics() async {
    try {
      return await _saleRepository.getSalesStatistics();
    } catch (e) {
      _setError('Failed to get sales statistics: ${e.toString()}');
      return {};
    }
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
  }

  // Clear sales list
  void clearSales() {
    _sales.clear();
    notifyListeners();
  }
}
