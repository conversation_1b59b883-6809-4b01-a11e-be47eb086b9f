import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:market/shared_widgets/wrappers.dart';
import '../providers/purchase_provider.dart';
import '../../domain/entities/purchase.dart';
import '../../../suppliers/presentation/providers/supplier_provider.dart';
import 'package:intl/intl.dart';

class PurchasesScreen extends StatefulWidget {
  const PurchasesScreen({super.key});

  @override
  State<PurchasesScreen> createState() => _PurchasesScreenState();
}

class _PurchasesScreenState extends State<PurchasesScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    // Load purchases when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<PurchaseProvider>().fetchPurchases();
      context.read<SupplierProvider>().loadSuppliers();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SecondaryScreenWrapper(
      title: 'المشتريات',
      actions: [
        IconButton(
          icon: const Icon(Icons.filter_list),
          onPressed: () => _showFilterDialog(context),
          tooltip: 'فلترة',
        ),
      ],
      floatingActionButton: FloatingActionButton(
        onPressed: () => context.go('/purchases/new'),
        backgroundColor: Colors.orange,
        child: const Icon(Icons.add, color: Colors.white),
      ),
      child: Consumer<PurchaseProvider>(
        builder: (context, purchaseProvider, child) {
          if (purchaseProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (purchaseProvider.errorMessage != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Theme.of(context).colorScheme.error,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'خطأ في تحميل المشتريات',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    purchaseProvider.errorMessage!,
                    style: Theme.of(context).textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => purchaseProvider.fetchPurchases(),
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            );
          }

          if (purchaseProvider.purchases.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.shopping_bag_outlined,
                    size: 64,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'لا يوجد مشتريات',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'اضغط على زر الإضافة لإضافة فاتورة مشتريات جديدة',
                    style: Theme.of(context).textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            );
          }

          return Column(
            children: [
              // Search bar
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'البحث في المشتريات...',
                    prefixIcon: const Icon(Icons.search),
                    border: const OutlineInputBorder(),
                    suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                              setState(() {
                                _searchQuery = '';
                              });
                            },
                          )
                        : null,
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
              ),
              // Purchases list
              Expanded(
                child: Builder(
                  builder: (context) {
                    final filteredPurchases = purchaseProvider
                        .getFilteredPurchases(_searchQuery);

                    if (filteredPurchases.isEmpty && _searchQuery.isNotEmpty) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.search_off,
                              size: 64,
                              color: Colors.grey[400],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'لا توجد نتائج للبحث',
                              style: Theme.of(context).textTheme.headlineSmall,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'جرب البحث بكلمات مختلفة',
                              style: Theme.of(context).textTheme.bodyMedium,
                            ),
                          ],
                        ),
                      );
                    }

                    return RefreshIndicator(
                      onRefresh: () async {
                        await purchaseProvider.fetchPurchases();
                      },
                      child: ListView.builder(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        itemCount: filteredPurchases.length,
                        itemBuilder: (context, index) {
                          final purchase = filteredPurchases[index];
                          return _buildPurchaseCard(context, purchase);
                        },
                      ),
                    );
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  void _showFilterDialog(BuildContext context) {
    final purchaseProvider = context.read<PurchaseProvider>();
    final supplierProvider = context.read<SupplierProvider>();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('فلترة المشتريات'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Payment Status Filter
              const Text(
                'حالة الدفع:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              DropdownButtonFormField<String?>(
                value: purchaseProvider.selectedPaymentStatus,
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                ),
                items: const [
                  DropdownMenuItem(value: null, child: Text('الكل')),
                  DropdownMenuItem(
                    value: 'مدفوعة بالكامل',
                    child: Text('مدفوعة بالكامل'),
                  ),
                  DropdownMenuItem(
                    value: 'مدفوعة جزئياً',
                    child: Text('مدفوعة جزئياً'),
                  ),
                  DropdownMenuItem(
                    value: 'غير مدفوعة',
                    child: Text('غير مدفوعة'),
                  ),
                ],
                onChanged: (value) {
                  purchaseProvider.filterPurchases(paymentStatus: value);
                },
              ),
              const SizedBox(height: 16),

              // Payment Method Filter
              const Text(
                'طريقة الدفع:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              DropdownButtonFormField<String?>(
                value: purchaseProvider.selectedPaymentMethod,
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                ),
                items: const [
                  DropdownMenuItem(value: null, child: Text('الكل')),
                  DropdownMenuItem(value: 'cash', child: Text('نقدي')),
                  DropdownMenuItem(value: 'credit', child: Text('آجل')),
                  DropdownMenuItem(value: 'card', child: Text('بطاقة ائتمان')),
                  DropdownMenuItem(
                    value: 'transfer',
                    child: Text('تحويل بنكي'),
                  ),
                ],
                onChanged: (value) {
                  purchaseProvider.filterPurchases(paymentMethod: value);
                },
              ),
              const SizedBox(height: 16),

              // Supplier Filter
              const Text(
                'المورد:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              DropdownButtonFormField<int?>(
                value: purchaseProvider.selectedSupplierId,
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                ),
                items: [
                  const DropdownMenuItem(value: null, child: Text('الكل')),
                  ...supplierProvider.suppliers.map((supplier) {
                    return DropdownMenuItem(
                      value: supplier.id,
                      child: Text(supplier.name),
                    );
                  }),
                ],
                onChanged: (value) {
                  purchaseProvider.filterPurchases(supplierId: value);
                },
              ),
              const SizedBox(height: 16),

              // Date Range Filter
              const Text(
                'التاريخ:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: TextButton.icon(
                      onPressed: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate:
                              purchaseProvider.selectedFromDate ??
                              DateTime.now(),
                          firstDate: DateTime(2020),
                          lastDate: DateTime.now(),
                        );
                        if (date != null) {
                          purchaseProvider.filterPurchases(fromDate: date);
                        }
                      },
                      icon: const Icon(Icons.calendar_today),
                      label: Text(
                        purchaseProvider.selectedFromDate != null
                            ? DateFormat(
                                'dd/MM/yyyy',
                              ).format(purchaseProvider.selectedFromDate!)
                            : 'من تاريخ',
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: TextButton.icon(
                      onPressed: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate:
                              purchaseProvider.selectedToDate ?? DateTime.now(),
                          firstDate: DateTime(2020),
                          lastDate: DateTime.now(),
                        );
                        if (date != null) {
                          purchaseProvider.filterPurchases(toDate: date);
                        }
                      },
                      icon: const Icon(Icons.calendar_today),
                      label: Text(
                        purchaseProvider.selectedToDate != null
                            ? DateFormat(
                                'dd/MM/yyyy',
                              ).format(purchaseProvider.selectedToDate!)
                            : 'إلى تاريخ',
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              purchaseProvider.clearFilters();
              Navigator.of(context).pop();
            },
            child: const Text('مسح الفلاتر'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildPurchaseCard(BuildContext context, Purchase purchase) {
    final dateFormat = DateFormat('dd/MM/yyyy HH:mm');

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: const CircleAvatar(
          backgroundColor: Colors.orange,
          child: Icon(Icons.shopping_bag, color: Colors.white),
        ),
        title: Text(
          'فاتورة رقم ${purchase.id}',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '🏪 ${purchase.supplierId != null ? "مورد رقم ${purchase.supplierId}" : "مورد غير محدد"}',
            ),
            Text('📅 ${dateFormat.format(purchase.purchaseDate)}'),
            Text('💳 ${purchase.paymentMethodDisplayName}'),
            Text('📊 ${purchase.statusDisplayName}'),
            Text('💰 ${purchase.paymentStatusText}'),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              '${purchase.totalAmount.toStringAsFixed(2)} ر.ي',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
                color: Colors.orange,
              ),
            ),
            PopupMenuButton<String>(
              onSelected: (value) {
                switch (value) {
                  case 'view':
                    context.go('/purchases/view/${purchase.id}');
                    break;
                  case 'edit':
                    context.go('/purchases/edit/${purchase.id}');
                    break;
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'view',
                  child: ListTile(
                    leading: Icon(Icons.visibility),
                    title: Text('عرض'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                const PopupMenuItem(
                  value: 'edit',
                  child: ListTile(
                    leading: Icon(Icons.edit),
                    title: Text('تعديل'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ],
            ),
          ],
        ),
        onTap: () => context.go('/purchases/view/${purchase.id}'),
      ),
    );
  }
}
