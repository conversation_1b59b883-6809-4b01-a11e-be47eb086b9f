import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import '../../domain/entities/order.dart';
import '../../domain/entities/order_item.dart';
import '../../domain/usecases/create_order.dart';
import '../../domain/usecases/get_all_orders.dart';
import '../../domain/usecases/get_order_by_id.dart';
import '../../domain/usecases/update_order.dart';
import '../../domain/usecases/delete_order.dart';
import '../../domain/usecases/generate_order_from_low_stock.dart';
import '../../../orders/domain/repositories/order_repository.dart';

class OrderProvider extends ChangeNotifier {
  final CreateOrderUseCase _createOrderUseCase;
  final GetAllOrdersUseCase _getAllOrdersUseCase;
  final GetOrderByIdUseCase _getOrderByIdUseCase;
  final UpdateOrderUseCase _updateOrderUseCase;
  final DeleteOrderUseCase _deleteOrderUseCase;
  final GenerateOrderFromLowStockUseCase _generateOrderFromLowStockUseCase;
  final OrderRepository _orderRepository;

  OrderProvider()
      : _createOrderUseCase = GetIt.instance<CreateOrderUseCase>(),
        _getAllOrdersUseCase = GetIt.instance<GetAllOrdersUseCase>(),
        _getOrderByIdUseCase = GetIt.instance<GetOrderByIdUseCase>(),
        _updateOrderUseCase = GetIt.instance<UpdateOrderUseCase>(),
        _deleteOrderUseCase = GetIt.instance<DeleteOrderUseCase>(),
        _generateOrderFromLowStockUseCase = GetIt.instance<GenerateOrderFromLowStockUseCase>(),
        _orderRepository = GetIt.instance<OrderRepository>();

  // State variables
  List<Order> _orders = [];
  bool _isLoading = false;
  String? _errorMessage;

  // Getters
  List<Order> get orders => _orders;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  // Fetch all orders
  Future<void> fetchOrders() async {
    _setLoading(true);
    _clearError();

    try {
      _orders = await _getAllOrdersUseCase.call();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load orders: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Create order
  Future<bool> createOrder(Order order, List<OrderItem> items) async {
    _setLoading(true);
    _clearError();

    try {
      await _createOrderUseCase.call(order, items);
      await fetchOrders(); // Refresh list
      return true;
    } catch (e) {
      _setError('Failed to create order: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Update order
  Future<bool> updateOrder(Order order) async {
    _setLoading(true);
    _clearError();

    try {
      await _updateOrderUseCase.call(order);
      await fetchOrders(); // Refresh list
      return true;
    } catch (e) {
      _setError('Failed to update order: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Delete order
  Future<bool> deleteOrder(int id) async {
    _setLoading(true);
    _clearError();

    try {
      await _deleteOrderUseCase.call(id);
      await fetchOrders(); // Refresh list
      return true;
    } catch (e) {
      _setError('Failed to delete order: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Get order by ID
  Future<Order?> getOrderById(int id) async {
    try {
      return await _getOrderByIdUseCase.call(id);
    } catch (e) {
      _setError('Failed to get order: ${e.toString()}');
      return null;
    }
  }

  // Get order items
  Future<List<OrderItem>> getOrderItems(int orderId) async {
    try {
      return await _orderRepository.getOrderItems(orderId);
    } catch (e) {
      _setError('Failed to get order items: ${e.toString()}');
      return [];
    }
  }

  // Generate order from low stock
  Future<Map<String, dynamic>?> generateOrderFromLowStock() async {
    _setLoading(true);
    _clearError();

    try {
      final result = await _generateOrderFromLowStockUseCase.call();
      return result;
    } catch (e) {
      _setError('Failed to generate order from low stock: ${e.toString()}');
      return null;
    } finally {
      _setLoading(false);
    }
  }

  // Get orders by status
  List<Order> getOrdersByStatus(String status) {
    return _orders.where((order) => order.status == status).toList();
  }

  // Get order statistics
  Future<Map<String, dynamic>> getOrderStatistics() async {
    try {
      return await _orderRepository.getOrderStatistics();
    } catch (e) {
      _setError('Failed to get order statistics: ${e.toString()}');
      return {};
    }
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
  }

  // Clear orders list
  void clearOrders() {
    _orders.clear();
    notifyListeners();
  }
}
