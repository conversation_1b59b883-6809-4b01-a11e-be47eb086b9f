import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:get_it/get_it.dart';
import 'package:market/features/dashboard/presentation/screens/dashboard_screen.dart';
import 'package:market/features/products/presentation/screens/products_screen.dart';
import 'package:market/features/products/presentation/screens/product_form_screen.dart';
import 'package:market/features/products/presentation/screens/product_details_screen.dart';
import 'package:market/features/customers/presentation/screens/customers_screen.dart';
import 'package:market/features/suppliers/presentation/screens/suppliers_screen.dart';
import 'package:market/features/sales/presentation/screens/sales_screen.dart';
import 'package:market/features/purchases/presentation/screens/purchases_screen.dart';
import 'package:market/features/expenses/presentation/screens/expenses_screen.dart';
import 'package:market/features/reports/presentation/screens/reports_screen.dart';
import 'package:market/features/settings/presentation/screens/settings_screen.dart';
import 'package:market/features/products/presentation/screens/internal_transfer_screen.dart';
import 'package:market/features/notifications/presentation/screens/notification_list_screen.dart';
import 'package:market/features/inventory_count/presentation/screens/inventory_count_list_screen.dart';
import 'package:market/features/inventory_count/presentation/screens/inventory_count_form_screen.dart';
import 'package:market/features/orders/presentation/screens/order_list_screen.dart';
import 'package:market/features/sales/presentation/screens/sale_form_screen.dart';
import 'package:market/features/purchases/presentation/screens/purchase_form_screen.dart';
import 'package:market/features/customers/presentation/screens/customer_form_screen.dart';
import 'package:market/features/suppliers/presentation/screens/supplier_form_screen.dart';
import 'package:market/features/expenses/presentation/screens/expense_form_screen.dart';
import 'package:market/features/orders/presentation/screens/order_form_screen.dart';
import 'package:market/features/customers/presentation/screens/customer_details_screen.dart';
import 'package:market/features/suppliers/presentation/screens/supplier_details_screen.dart';
import 'package:market/features/sales/presentation/screens/sale_details_screen.dart';
import 'package:market/features/purchases/presentation/screens/purchase_details_screen.dart';
import 'package:market/features/expenses/presentation/screens/expense_details_screen.dart';
import 'package:market/features/orders/presentation/screens/order_details_screen.dart';
import 'package:market/features/inventory_count/presentation/screens/inventory_count_details_screen.dart';
import 'package:market/features/customers/presentation/screens/customer_statements_screen.dart';
import 'package:market/features/suppliers/presentation/screens/supplier_statements_screen.dart';
import 'package:market/shared_widgets/placeholder_screen.dart';
import 'package:market/shared_widgets/wrappers.dart';

class AppRouter {
  static final GoRouter router = GoRouter(
    navigatorKey: GetIt.instance<GlobalKey<NavigatorState>>(),
    initialLocation: '/',
    routes: [
      GoRoute(path: '/', builder: (context, state) => const DashboardScreen()),
      GoRoute(
        path: '/products',
        builder: (context, state) => const ProductsScreen(),
        routes: [
          GoRoute(
            path: '/add',
            builder: (context, state) => const ProductFormScreen(),
          ),
          GoRoute(
            path: '/edit/:id',
            builder: (context, state) {
              final id = int.parse(state.pathParameters['id']!);
              return ProductFormScreen(productId: id);
            },
          ),
          GoRoute(
            path: '/:id',
            builder: (context, state) {
              final id = int.parse(state.pathParameters['id']!);
              return ProductDetailsScreen(productId: id);
            },
          ),
        ],
      ),
      GoRoute(
        path: '/customers',
        builder: (context, state) => const CustomersScreen(),
        routes: [
          GoRoute(
            path: '/new',
            builder: (context, state) => const CustomerFormScreen(),
          ),
          GoRoute(
            path: '/edit/:id',
            builder: (context, state) {
              final id = state.pathParameters['id']!;
              return CustomerFormScreen(customerId: id);
            },
          ),
          GoRoute(
            path: '/view/:id',
            builder: (context, state) {
              final id = int.parse(state.pathParameters['id']!);
              return CustomerDetailsScreen(customerId: id);
            },
          ),
        ],
      ),
      GoRoute(
        path: '/suppliers',
        builder: (context, state) => const SuppliersScreen(),
        routes: [
          GoRoute(
            path: '/new',
            builder: (context, state) => const SupplierFormScreen(),
          ),
          GoRoute(
            path: '/edit/:id',
            builder: (context, state) {
              final id = state.pathParameters['id']!;
              return SupplierFormScreen(supplierId: id);
            },
          ),
          GoRoute(
            path: '/view/:id',
            builder: (context, state) {
              final id = int.parse(state.pathParameters['id']!);
              return SupplierDetailsScreen(supplierId: id);
            },
          ),
        ],
      ),
      GoRoute(
        path: '/sales',
        builder: (context, state) => const SalesScreen(),
        routes: [
          GoRoute(
            path: '/new',
            builder: (context, state) => const SaleFormScreen(),
          ),
          GoRoute(
            path: '/edit/:id',
            builder: (context, state) {
              final id = int.parse(state.pathParameters['id']!);
              return SaleFormScreen(saleId: id);
            },
          ),
          GoRoute(
            path: '/view/:id',
            builder: (context, state) {
              final id = int.parse(state.pathParameters['id']!);
              return SaleDetailsScreen(saleId: id);
            },
          ),
        ],
      ),
      GoRoute(
        path: '/purchases',
        builder: (context, state) => const PurchasesScreen(),
        routes: [
          GoRoute(
            path: '/new',
            builder: (context, state) => const PurchaseFormScreen(),
          ),
          GoRoute(
            path: '/edit/:id',
            builder: (context, state) {
              final id = int.parse(state.pathParameters['id']!);
              return PurchaseFormScreen(purchaseId: id);
            },
          ),
          GoRoute(
            path: '/view/:id',
            builder: (context, state) {
              final id = int.parse(state.pathParameters['id']!);
              return PurchaseDetailsScreen(purchaseId: id);
            },
          ),
        ],
      ),
      GoRoute(
        path: '/transfers',
        builder: (context, state) => const InternalTransferScreen(),
      ),
      GoRoute(
        path: '/notifications',
        builder: (context, state) => const NotificationListScreen(),
      ),
      GoRoute(
        path: '/orders',
        builder: (context, state) => const OrderListScreen(),
        routes: [
          GoRoute(
            path: '/new',
            builder: (context, state) => const OrderFormScreen(),
          ),
          GoRoute(
            path: '/edit/:id',
            builder: (context, state) {
              final id = int.parse(state.pathParameters['id']!);
              return OrderFormScreen(orderId: id);
            },
          ),
          GoRoute(
            path: '/view/:id',
            builder: (context, state) {
              final id = int.parse(state.pathParameters['id']!);
              return OrderDetailsScreen(orderId: id);
            },
          ),
        ],
      ),

      // Inventory Count Routes
      GoRoute(
        path: '/inventory_count',
        builder: (context, state) => const InventoryCountListScreen(),
      ),
      GoRoute(
        path: '/inventory_count/new',
        builder: (context, state) => const InventoryCountFormScreen(),
      ),
      GoRoute(
        path: '/inventory_count/:id',
        builder: (context, state) {
          final id = int.parse(state.pathParameters['id']!);
          return InventoryCountDetailsScreen(inventoryCountId: id);
        },
      ),

      // Legacy routes - now handled by nested routes above

      // Expenses Routes
      GoRoute(
        path: '/expenses',
        builder: (context, state) => const ExpensesScreen(),
        routes: [
          GoRoute(
            path: '/new',
            builder: (context, state) => const ExpenseFormScreen(),
          ),
          GoRoute(
            path: '/edit/:id',
            builder: (context, state) {
              final id = state.pathParameters['id']!;
              return ExpenseFormScreen(expenseId: id);
            },
          ),
          GoRoute(
            path: '/view/:id',
            builder: (context, state) {
              final id = int.parse(state.pathParameters['id']!);
              return ExpenseDetailsScreen(expenseId: id);
            },
          ),
        ],
      ),

      GoRoute(
        path: '/customer_statements',
        builder: (context, state) => const CustomerStatementsScreen(),
      ),
      GoRoute(
        path: '/supplier_statements',
        builder: (context, state) => const SupplierStatementsScreen(),
      ),
      GoRoute(
        path: '/reports',
        builder: (context, state) => const ReportsScreen(),
      ),
      GoRoute(
        path: '/settings',
        builder: (context, state) => const SettingsScreen(),
      ),
    ],
  );
}
