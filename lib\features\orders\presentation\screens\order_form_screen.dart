import 'package:flutter/material.dart';

import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:market/shared_widgets/wrappers.dart';
import 'package:market/features/products/presentation/providers/product_provider.dart';
import 'package:market/features/products/domain/entities/product.dart';
import 'package:market/features/orders/presentation/providers/order_provider.dart';
import 'package:market/features/orders/domain/entities/order.dart';
import 'package:market/features/orders/domain/entities/order_item.dart';

// Helper class for UI display
class OrderItemDisplay {
  final int? productId;
  final String productName;
  final int quantity;
  final double estimatedUnitPrice;

  OrderItemDisplay({
    this.productId,
    required this.productName,
    required this.quantity,
    required this.estimatedUnitPrice,
  });

  double get estimatedTotal => quantity * estimatedUnitPrice;
}

class OrderFormScreen extends StatefulWidget {
  const OrderFormScreen({super.key});

  @override
  State<OrderFormScreen> createState() => _OrderFormScreenState();
}

class _OrderFormScreenState extends State<OrderFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _notesController = TextEditingController();

  DateTime _selectedDate = DateTime.now();
  final List<OrderItemDisplay> _orderItems = [];
  bool _isLoading = false;

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  double get _totalEstimatedAmount {
    double total = 0;
    for (var item in _orderItems) {
      total += item.estimatedTotal;
    }
    return total;
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      locale: const Locale('ar'),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _createOrderFromLowStock() async {
    setState(() => _isLoading = true);
    try {
      final productProvider = context.read<ProductProvider>();
      await productProvider.fetchProducts();
      final allProducts = productProvider.products;

      // Filter products with low stock
      final lowStockProducts = allProducts
          .where((product) => product.totalQuantity <= product.minStockQuantity)
          .toList();

      setState(() {
        _orderItems.clear();
        for (var product in lowStockProducts) {
          final neededQuantity =
              product.minStockQuantity -
              product.totalQuantity +
              5; // Add buffer
          _orderItems.add(
            OrderItemDisplay(
              productId: product.id,
              productName: product.name,
              quantity: neededQuantity.toInt(),
              estimatedUnitPrice: product.lastPurchasePrice ?? 0.0,
            ),
          );
        }
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم إضافة ${lowStockProducts.length} منتج من المخزون المنخفض',
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في تحميل المنتجات: $e')));
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _addOrderItem() {
    showDialog(
      context: context,
      builder: (context) => _AddOrderItemDialog(
        onItemAdded: (item) {
          setState(() {
            _orderItems.add(item);
          });
        },
      ),
    );
  }

  void _editItem(int index) {
    final item = _orderItems[index];
    showDialog(
      context: context,
      builder: (context) => _AddOrderItemDialog(
        onItemAdded: (editedItem) {
          setState(() {
            _orderItems[index] = editedItem;
          });
        },
        initialItem: item,
      ),
    );
  }

  void _removeItem(int index) {
    setState(() {
      _orderItems.removeAt(index);
    });
  }

  Future<void> _saveOrder() async {
    if (!_formKey.currentState!.validate()) return;

    if (_orderItems.isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('يرجى إضافة عناصر للطلبية')));
      return;
    }

    setState(() => _isLoading = true);
    try {
      // Create Order entity
      final order = Order(
        orderDate: _selectedDate,
        totalEstimatedCost: _totalEstimatedAmount,
        notes: _notesController.text.trim().isEmpty
            ? null
            : _notesController.text.trim(),
        status: 'pending',
      );

      // Create OrderItem entities
      final orderItems = _orderItems
          .map(
            (item) => OrderItem(
              orderId: 0, // Will be set by the database
              productId: item.productId!,
              quantity: item.quantity,
              estimatedUnitPrice: item.estimatedUnitPrice,
            ),
          )
          .toList();

      await context.read<OrderProvider>().createOrder(order, orderItems);

      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('تم إنشاء الطلبية بنجاح')));
        context.pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في حفظ الطلبية: $e')));
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return SecondaryScreenWrapper(
      title: 'إنشاء طلبية شراء',
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // التاريخ
              Card(
                child: ListTile(
                  leading: const Icon(Icons.calendar_today),
                  title: const Text('تاريخ الطلبية'),
                  subtitle: Text(
                    '${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: _selectDate,
                ),
              ),
              const SizedBox(height: 16),

              // إنشاء من المخزون المنخفض
              Card(
                color: Colors.orange.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'إنشاء سريع',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        'يمكنك إنشاء طلبية تلقائياً من المنتجات ذات المخزون المنخفض',
                        style: TextStyle(fontSize: 14),
                      ),
                      const SizedBox(height: 12),
                      ElevatedButton.icon(
                        onPressed: _isLoading ? null : _createOrderFromLowStock,
                        icon: const Icon(Icons.auto_awesome),
                        label: const Text('إنشاء من المخزون المنخفض'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // إضافة عناصر
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            'عناصر الطلبية',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          ElevatedButton.icon(
                            onPressed: _addOrderItem,
                            icon: const Icon(Icons.add),
                            label: const Text('إضافة منتج'),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      if (_orderItems.isEmpty)
                        const Center(
                          child: Padding(
                            padding: EdgeInsets.all(20),
                            child: Text('لم يتم إضافة أي عناصر بعد'),
                          ),
                        )
                      else
                        ...List.generate(_orderItems.length, (index) {
                          final item = _orderItems[index];
                          return Card(
                            margin: const EdgeInsets.only(bottom: 8),
                            child: ListTile(
                              title: Text(item.productName),
                              subtitle: Text(
                                'الكمية: ${item.quantity} × ${item.estimatedUnitPrice.toStringAsFixed(2)} ريال',
                              ),
                              trailing: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    '${item.estimatedTotal.toStringAsFixed(2)} ريال',
                                    style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  IconButton(
                                    icon: const Icon(
                                      Icons.edit,
                                      color: Colors.blue,
                                    ),
                                    onPressed: () => _editItem(index),
                                  ),
                                  IconButton(
                                    icon: const Icon(
                                      Icons.delete,
                                      color: Colors.red,
                                    ),
                                    onPressed: () => _removeItem(index),
                                  ),
                                ],
                              ),
                            ),
                          );
                        }),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // ملاحظات
              TextFormField(
                controller: _notesController,
                decoration: const InputDecoration(
                  labelText: 'ملاحظات',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.note),
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 16),

              // الإجمالي التقديري
              Card(
                color: Colors.blue.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'الإجمالي التقديري:',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '${_totalEstimatedAmount.toStringAsFixed(2)} ريال',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue.shade700,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),

              // زر الحفظ
              ElevatedButton(
                onPressed: _isLoading ? null : _saveOrder,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: _isLoading
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                      )
                    : const Text(
                        'حفظ الطلبية',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
              ),
              const SizedBox(height: 16),

              // ملاحظة
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  border: Border.all(color: Colors.blue.shade200),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info_outline, color: Colors.blue.shade700),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'ملاحظة: وحدة الطلبيات قيد التطوير. هذا النموذج للعرض فقط.',
                        style: TextStyle(
                          color: Colors.blue.shade700,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _AddOrderItemDialog extends StatefulWidget {
  final Function(OrderItemDisplay) onItemAdded;
  final OrderItemDisplay? initialItem;

  const _AddOrderItemDialog({required this.onItemAdded, this.initialItem});

  @override
  State<_AddOrderItemDialog> createState() => _AddOrderItemDialogState();
}

class _AddOrderItemDialogState extends State<_AddOrderItemDialog> {
  final _quantityController = TextEditingController();
  final _priceController = TextEditingController();
  Product? _selectedProduct;
  List<Product> _products = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    // If editing, set initial values
    if (widget.initialItem != null) {
      _quantityController.text = widget.initialItem!.quantity.toString();
      _priceController.text = widget.initialItem!.estimatedUnitPrice.toString();
    }
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadProducts();
    });
  }

  @override
  void dispose() {
    _quantityController.dispose();
    _priceController.dispose();
    super.dispose();
  }

  Future<void> _loadProducts() async {
    try {
      final productProvider = context.read<ProductProvider>();
      await productProvider.fetchProducts();
      if (mounted) {
        final products = productProvider.products;
        setState(() {
          _products = products;
          _isLoading = false;

          // If editing, find and select the initial product
          if (widget.initialItem != null &&
              widget.initialItem!.productId != null) {
            _selectedProduct = products.firstWhere(
              (p) => p.id == widget.initialItem!.productId,
              orElse: () => products.first,
            );
          }
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في تحميل المنتجات: $e')));
      }
    }
  }

  void _selectProduct(Product product) {
    setState(() {
      _selectedProduct = product;
      _priceController.text = product.lastPurchasePrice?.toString() ?? '0.0';
    });
  }

  void _addItem() {
    if (_selectedProduct == null) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('يرجى اختيار منتج')));
      return;
    }

    if (_quantityController.text.trim().isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('يرجى إدخال الكمية')));
      return;
    }

    final quantity = double.tryParse(_quantityController.text.trim());
    final price = double.tryParse(_priceController.text.trim());

    if (quantity == null || quantity <= 0 || price == null || price <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى إدخال قيم صحيحة للكمية والسعر')),
      );
      return;
    }

    final item = OrderItemDisplay(
      productId: _selectedProduct!.id,
      productName: _selectedProduct!.name,
      quantity: quantity.toInt(),
      estimatedUnitPrice: price,
    );

    widget.onItemAdded(item);
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(
        widget.initialItem != null ? 'تعديل المنتج' : 'إضافة منتج للطلبية',
      ),
      content: SizedBox(
        width: double.maxFinite,
        height: 400,
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : Column(
                children: [
                  // اختيار المنتج
                  Expanded(
                    child: ListView.builder(
                      itemCount: _products.length,
                      itemBuilder: (context, index) {
                        final product = _products[index];
                        final isSelected = _selectedProduct?.id == product.id;
                        return Card(
                          color: isSelected ? Colors.blue.shade50 : null,
                          child: ListTile(
                            title: Text(product.name),
                            subtitle: Text(
                              'آخر سعر شراء: ${product.lastPurchasePrice?.toStringAsFixed(2) ?? 'غير محدد'} ريال\n'
                              'المخزون الحالي: ${product.totalQuantity}',
                            ),
                            trailing: isSelected
                                ? const Icon(
                                    Icons.check_circle,
                                    color: Colors.blue,
                                  )
                                : null,
                            onTap: () => _selectProduct(product),
                          ),
                        );
                      },
                    ),
                  ),
                  const SizedBox(height: 16),
                  // الكمية والسعر
                  if (_selectedProduct != null) ...[
                    TextField(
                      controller: _quantityController,
                      decoration: const InputDecoration(
                        labelText: 'الكمية المطلوبة',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: _priceController,
                      decoration: const InputDecoration(
                        labelText: 'السعر التقديري',
                        border: OutlineInputBorder(),
                        suffixText: 'ريال',
                      ),
                      keyboardType: TextInputType.number,
                    ),
                  ],
                ],
              ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        if (_selectedProduct != null)
          ElevatedButton(
            onPressed: _addItem,
            child: Text(widget.initialItem != null ? 'تحديث' : 'إضافة'),
          ),
      ],
    );
  }
}
