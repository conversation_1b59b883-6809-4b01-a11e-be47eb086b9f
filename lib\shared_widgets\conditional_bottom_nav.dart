import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../features/notifications/presentation/providers/notification_provider.dart';

class ConditionalBottomNav extends StatelessWidget {
  const ConditionalBottomNav({super.key});

  // Routes that should show bottom navigation
  static const List<String> _bottomNavRoutes = [
    '/',
    '/products',
    '/customers',
    '/sales',
    '/reports',
    '/notifications',
  ];

  @override
  Widget build(BuildContext context) {
    final currentRoute = GoRouterState.of(context).fullPath ?? '/';

    // Only show bottom nav for specific routes
    if (!_bottomNavRoutes.contains(currentRoute)) {
      return const SizedBox.shrink();
    }

    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      currentIndex: _getCurrentIndex(currentRoute),
      onTap: (index) => _onTap(context, index),
      selectedItemColor: Theme.of(context).colorScheme.primary,
      unselectedItemColor: Theme.of(
        context,
      ).colorScheme.onSurface.withValues(alpha: 0.6),
      items: const [
        BottomNavigationBarItem(icon: Icon(Icons.dashboard), label: 'الرئيسية'),
        BottomNavigationBarItem(icon: Icon(Icons.inventory), label: 'المنتجات'),
        BottomNavigationBarItem(icon: Icon(Icons.people), label: 'العملاء'),
        BottomNavigationBarItem(
          icon: Icon(Icons.point_of_sale),
          label: 'المبيعات',
        ),
        BottomNavigationBarItem(icon: Icon(Icons.analytics), label: 'التقارير'),
      ],
    );
  }

  int _getCurrentIndex(String currentRoute) {
    switch (currentRoute) {
      case '/':
        return 0;
      case '/products':
        return 1;
      case '/customers':
        return 2;
      case '/sales':
        return 3;
      case '/reports':
        return 4;
      default:
        return 0;
    }
  }

  void _onTap(BuildContext context, int index) {
    switch (index) {
      case 0:
        context.go('/');
        break;
      case 1:
        context.go('/products');
        break;
      case 2:
        context.go('/customers');
        break;
      case 3:
        context.go('/sales');
        break;
      case 4:
        context.go('/reports');
        break;
    }
  }
}
