import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:file_picker/file_picker.dart';
import 'package:market/shared_widgets/wrappers.dart';
import 'package:market/core/backup/backup_service.dart';
import 'package:market/features/products/presentation/providers/product_provider.dart';
import 'package:market/features/customers/presentation/providers/customer_provider.dart';
import 'package:market/features/suppliers/presentation/providers/supplier_provider.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return SecondaryScreenWrapper(
      title: 'الإعدادات',
      child: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // Import Section
          _buildSectionHeader('استيراد البيانات'),
          _buildImportTileWithInfo(
            context,
            title: 'استيراد المنتجات',
            subtitle: 'استيراد المنتجات من ملف Excel',
            icon: Icons.inventory_2,
            onTap: () => _importProducts(context),
            onInfoTap: () => _showExcelFormatInfo(context, 'products'),
          ),
          _buildImportTileWithInfo(
            context,
            title: 'استيراد العملاء',
            subtitle: 'استيراد العملاء من ملف Excel',
            icon: Icons.people,
            onTap: () => _importCustomers(context),
            onInfoTap: () => _showExcelFormatInfo(context, 'customers'),
          ),
          _buildImportTile(
            context,
            title: 'استيراد العملاء من جهات الاتصال',
            subtitle: 'استيراد العملاء من جهات اتصال الجهاز',
            icon: Icons.contact_phone,
            onTap: () => _importCustomersFromContacts(context),
          ),
          _buildImportTileWithInfo(
            context,
            title: 'استيراد الموردين',
            subtitle: 'استيراد الموردين من ملف Excel',
            icon: Icons.business,
            onTap: () => _importSuppliers(context),
            onInfoTap: () => _showExcelFormatInfo(context, 'suppliers'),
          ),
          _buildImportTile(
            context,
            title: 'استيراد الموردين من جهات الاتصال',
            subtitle: 'استيراد الموردين من جهات اتصال الجهاز',
            icon: Icons.business_center,
            onTap: () => _importSuppliersFromContacts(context),
          ),

          const SizedBox(height: 24),

          // Backup Section
          _buildSectionHeader('النسخ الاحتياطي والاستعادة'),
          _buildBackupTile(
            context,
            title: 'نسخ احتياطي محلي',
            subtitle: 'إنشاء نسخة احتياطية محلية',
            icon: Icons.backup,
            onTap: () => _performLocalBackup(context),
          ),
          _buildBackupTile(
            context,
            title: 'استعادة من نسخة محلية',
            subtitle: 'استعادة البيانات من نسخة احتياطية محلية',
            icon: Icons.restore,
            onTap: () => _restoreLocalBackup(context),
          ),
          _buildBackupTile(
            context,
            title: 'نسخ احتياطي سحابي',
            subtitle: 'نسخ احتياطي على Google Drive',
            icon: Icons.cloud_upload,
            onTap: () => _performCloudBackup(context),
          ),
          _buildBackupTile(
            context,
            title: 'استعادة من السحابة',
            subtitle: 'استعادة من Google Drive',
            icon: Icons.cloud_download,
            onTap: () => _restoreCloudBackup(context),
          ),
          _buildBackupTile(
            context,
            title: 'إعدادات النسخ التلقائي',
            subtitle: 'تفعيل/إلغاء النسخ الاحتياطي التلقائي',
            icon: Icons.schedule,
            onTap: () => _configureAutomaticBackup(context),
          ),

          const SizedBox(height: 24),

          // App Info Section
          _buildSectionHeader('معلومات التطبيق'),
          ListTile(
            leading: const Icon(Icons.info),
            title: const Text('إصدار التطبيق'),
            subtitle: const Text('1.0.0'),
            trailing: const Icon(Icons.chevron_right),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Colors.blue,
        ),
      ),
    );
  }

  Widget _buildImportTile(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: ListTile(
        leading: Icon(icon, color: Colors.green),
        title: Text(title),
        subtitle: Text(subtitle),
        trailing: const Icon(Icons.file_upload),
        onTap: onTap,
      ),
    );
  }

  Widget _buildImportTileWithInfo(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
    required VoidCallback onInfoTap,
  }) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: ListTile(
        leading: Icon(icon, color: Colors.green),
        title: Text(title),
        subtitle: Text(subtitle),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.info_outline, color: Colors.blue),
              onPressed: onInfoTap,
              tooltip: 'معلومات تنسيق الملف',
            ),
            const Icon(Icons.file_upload),
          ],
        ),
        onTap: onTap,
      ),
    );
  }

  Widget _buildBackupTile(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: ListTile(
        leading: Icon(icon, color: Colors.blue),
        title: Text(title),
        subtitle: Text(subtitle),
        trailing: const Icon(Icons.chevron_right),
        onTap: onTap,
      ),
    );
  }

  // Excel Format Info Methods
  void _showExcelFormatInfo(BuildContext context, String type) {
    String title;
    List<String> columns;

    switch (type) {
      case 'products':
        title = 'تنسيق ملف Excel للمنتجات';
        columns = [
          'name - اسم المنتج (مطلوب)',
          'description - الوصف',
          'category - الفئة (مطلوب)',
          'unit - الوحدة (مطلوب)',
          'lastPurchasePrice - آخر سعر شراء',
          'wholesalePrice - سعر الجملة (مطلوب)',
          'retailPrice - سعر التجزئة (مطلوب)',
          'minStockQuantity - الحد الأدنى للمخزون (مطلوب)',
          'barcode - الباركود',
          'warehouseQuantity - كمية المخزن (مطلوب)',
          'storeQuantity - كمية المحل (مطلوب)',
        ];
        break;
      case 'customers':
        title = 'تنسيق ملف Excel للعملاء';
        columns = [
          'name - اسم العميل (مطلوب)',
          'phone - رقم الهاتف',
          'email - البريد الإلكتروني',
          'address - العنوان',
          'creditLimit - الحد الائتماني',
          'notes - ملاحظات',
        ];
        break;
      case 'suppliers':
        title = 'تنسيق ملف Excel للموردين';
        columns = [
          'name - اسم المورد (مطلوب)',
          'phone - رقم الهاتف',
          'email - البريد الإلكتروني',
          'address - العنوان',
          'contactPerson - الشخص المسؤول',
          'notes - ملاحظات',
        ];
        break;
      default:
        return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'يجب أن يحتوي الملف على الأعمدة التالية في الصف الأول:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              ...columns.map(
                (column) => Padding(
                  padding: const EdgeInsets.symmetric(vertical: 2),
                  child: Text('• $column'),
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                'ملاحظات:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              const Text('• الحقول المطلوبة يجب ملؤها'),
              const Text('• استخدم تنسيق .xlsx أو .xls'),
              const Text('• تأكد من عدم وجود مسافات زائدة في أسماء الأعمدة'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  // Import from Contacts Methods
  Future<void> _importCustomersFromContacts(BuildContext context) async {
    try {
      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('جاري استيراد العملاء من جهات الاتصال...'),
            ],
          ),
        ),
      );

      final customerProvider = context.read<CustomerProvider>();
      final result = await customerProvider.importFromContacts();

      if (!context.mounted) return;
      Navigator.of(context).pop(); // Close loading dialog

      _showSuccessDialog(
        context,
        'تم استيراد العملاء',
        'تم استيراد $result عميل من جهات الاتصال بنجاح',
      );
    } catch (e) {
      if (!context.mounted) return;
      Navigator.of(context).pop(); // Close loading dialog
      _showErrorDialog(context, 'خطأ في استيراد العملاء', e.toString());
    }
  }

  Future<void> _importSuppliersFromContacts(BuildContext context) async {
    try {
      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('جاري استيراد الموردين من جهات الاتصال...'),
            ],
          ),
        ),
      );

      final supplierProvider = context.read<SupplierProvider>();
      final result = await supplierProvider.importFromContacts();

      if (!context.mounted) return;
      Navigator.of(context).pop(); // Close loading dialog

      _showSuccessDialog(
        context,
        'تم استيراد الموردين',
        'تم استيراد $result مورد من جهات الاتصال بنجاح',
      );
    } catch (e) {
      if (!context.mounted) return;
      Navigator.of(context).pop(); // Close loading dialog
      _showErrorDialog(context, 'خطأ في استيراد الموردين', e.toString());
    }
  }

  // Import Methods
  Future<void> _importProducts(BuildContext context) async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['xlsx', 'xls'],
      );

      if (result != null && result.files.single.path != null) {
        final file = File(result.files.single.path!);

        if (!context.mounted) return;

        // Show loading dialog
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => const AlertDialog(
            content: Row(
              children: [
                CircularProgressIndicator(),
                SizedBox(width: 16),
                Text('جاري استيراد المنتجات...'),
              ],
            ),
          ),
        );

        final productProvider = context.read<ProductProvider>();
        final importResult = await productProvider.importProducts(file);

        if (!context.mounted) return;
        Navigator.of(context).pop(); // Close loading dialog

        // Show result dialog
        _showImportResultDialog(context, 'المنتجات', importResult);
      }
    } catch (e) {
      if (!context.mounted) return;
      _showErrorDialog(context, 'خطأ في استيراد المنتجات', e.toString());
    }
  }

  Future<void> _importCustomers(BuildContext context) async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['xlsx', 'xls'],
      );

      if (result != null && result.files.single.path != null) {
        final file = File(result.files.single.path!);

        if (!context.mounted) return;

        // Show loading dialog
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => const AlertDialog(
            content: Row(
              children: [
                CircularProgressIndicator(),
                SizedBox(width: 16),
                Text('جاري استيراد العملاء...'),
              ],
            ),
          ),
        );

        final customerProvider = context.read<CustomerProvider>();
        final importResult = await customerProvider.importCustomers(file);

        if (!context.mounted) return;
        Navigator.of(context).pop(); // Close loading dialog

        // Show result dialog
        _showImportResultDialog(context, 'العملاء', importResult);
      }
    } catch (e) {
      if (!context.mounted) return;
      _showErrorDialog(context, 'خطأ في استيراد العملاء', e.toString());
    }
  }

  Future<void> _importSuppliers(BuildContext context) async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['xlsx', 'xls'],
      );

      if (result != null && result.files.single.path != null) {
        final file = File(result.files.single.path!);

        if (!context.mounted) return;

        // Show loading dialog
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => const AlertDialog(
            content: Row(
              children: [
                CircularProgressIndicator(),
                SizedBox(width: 16),
                Text('جاري استيراد الموردين...'),
              ],
            ),
          ),
        );

        final supplierProvider = context.read<SupplierProvider>();
        final importResult = await supplierProvider.importSuppliers(file);

        if (!context.mounted) return;
        Navigator.of(context).pop(); // Close loading dialog

        // Show result dialog
        _showImportResultDialog(context, 'الموردين', importResult);
      }
    } catch (e) {
      if (!context.mounted) return;
      _showErrorDialog(context, 'خطأ في استيراد الموردين', e.toString());
    }
  }

  // Backup Methods
  Future<void> _performLocalBackup(BuildContext context) async {
    try {
      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('جاري إنشاء النسخة الاحتياطية...'),
            ],
          ),
        ),
      );

      final backupPath = await BackupService.instance.performLocalBackup();

      if (!context.mounted) return;
      Navigator.of(context).pop(); // Close loading dialog

      _showSuccessDialog(
        context,
        'تم إنشاء النسخة الاحتياطية',
        'تم حفظ النسخة الاحتياطية في:\n$backupPath',
      );
    } catch (e) {
      if (!context.mounted) return;
      Navigator.of(context).pop(); // Close loading dialog
      _showErrorDialog(context, 'خطأ في النسخ الاحتياطي', e.toString());
    }
  }

  Future<void> _restoreLocalBackup(BuildContext context) async {
    try {
      final backups = await BackupService.instance.getLocalBackups();

      if (backups.isEmpty) {
        _showErrorDialog(
          context,
          'لا توجد نسخ احتياطية',
          'لم يتم العثور على نسخ احتياطية محلية',
        );
        return;
      }

      // Show backup selection dialog
      final selectedBackup = await _showBackupSelectionDialog(context, backups);
      if (selectedBackup == null) return;

      // Show confirmation dialog
      final confirmed = await _showConfirmationDialog(
        context,
        'تأكيد الاستعادة',
        'هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟ سيتم استبدال جميع البيانات الحالية.',
      );
      if (!confirmed) return;

      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('جاري استعادة البيانات...'),
            ],
          ),
        ),
      );

      await BackupService.instance.restoreLocalBackup(selectedBackup.path);

      if (!context.mounted) return;
      Navigator.of(context).pop(); // Close loading dialog

      _showSuccessDialog(
        context,
        'تم استعادة البيانات',
        'تم استعادة البيانات بنجاح من النسخة الاحتياطية',
      );
    } catch (e) {
      if (!context.mounted) return;
      Navigator.of(context).pop(); // Close loading dialog
      _showErrorDialog(context, 'خطأ في الاستعادة', e.toString());
    }
  }

  Future<void> _performCloudBackup(BuildContext context) async {
    _showErrorDialog(
      context,
      'ميزة غير متوفرة',
      'النسخ الاحتياطي السحابي غير مفعل حالياً. سيتم إضافته في التحديثات القادمة.',
    );
  }

  Future<void> _restoreCloudBackup(BuildContext context) async {
    _showErrorDialog(
      context,
      'ميزة غير متوفرة',
      'الاستعادة من السحابة غير مفعلة حالياً. سيتم إضافتها في التحديثات القادمة.',
    );
  }

  Future<void> _configureAutomaticBackup(BuildContext context) async {
    _showErrorDialog(
      context,
      'ميزة غير متوفرة',
      'إعدادات النسخ التلقائي غير مفعلة حالياً. سيتم إضافتها في التحديثات القادمة.',
    );
  }

  // Helper Methods
  void _showImportResultDialog(
    BuildContext context,
    String dataType,
    dynamic importResult,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('نتيجة استيراد $dataType'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(importResult.summary),
            if (importResult.hasErrors) ...[
              const SizedBox(height: 16),
              const Text(
                'الأخطاء:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              ...importResult.errors.map((error) => Text('• $error')),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showSuccessDialog(BuildContext context, String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(BuildContext context, String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  Future<FileSystemEntity?> _showBackupSelectionDialog(
    BuildContext context,
    List<FileSystemEntity> backups,
  ) async {
    return showDialog<FileSystemEntity>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر النسخة الاحتياطية'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: backups.length,
            itemBuilder: (context, index) {
              final backup = backups[index];
              final fileName = backup.path.split('/').last;
              final stat = File(backup.path).statSync();

              return ListTile(
                title: Text(fileName),
                subtitle: Text('تاريخ الإنشاء: ${stat.modified}'),
                onTap: () => Navigator.of(context).pop(backup),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  Future<bool> _showConfirmationDialog(
    BuildContext context,
    String title,
    String message,
  ) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );
    return result ?? false;
  }
}
