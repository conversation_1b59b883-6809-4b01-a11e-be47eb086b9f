import '../entities/sale.dart';
import '../repositories/sale_repository.dart';

class UpdateSaleUseCase {
  final SaleRepository _repository;

  UpdateSaleUseCase(this._repository);

  Future<void> call(Sale sale) async {
    // Validation
    if (sale.id == null || sale.id! <= 0) {
      throw Exception('Sale ID must be provided and greater than 0');
    }

    if (sale.totalAmount < 0) {
      throw Exception('Total amount cannot be negative');
    }

    if (sale.paidAmount < 0) {
      throw Exception('Paid amount cannot be negative');
    }

    if (sale.dueAmount < 0) {
      throw Exception('Due amount cannot be negative');
    }

    if (sale.paymentMethod.isEmpty) {
      throw Exception('Payment method cannot be empty');
    }

    if (sale.status.isEmpty) {
      throw Exception('Sale status cannot be empty');
    }

    // Validate payment amounts
    if ((sale.paidAmount + sale.dueAmount - sale.totalAmount).abs() > 0.01) {
      throw Exception('Paid amount + due amount must equal total amount');
    }

    try {
      await _repository.updateSale(sale);
    } catch (e) {
      throw Exception('Failed to update sale: $e');
    }
  }
}
