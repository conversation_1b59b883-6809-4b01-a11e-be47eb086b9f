import 'package:sqflite/sqflite.dart';
import '../../../../core/database/database_service.dart';
import '../models/inventory_count_model.dart';

class InventoryCountDatabaseService {
  final DatabaseService _databaseService;

  InventoryCountDatabaseService(this._databaseService);

  /// Create a new inventory count
  Future<int> createInventoryCount(InventoryCountModel inventoryCount) async {
    try {
      final db = await _databaseService.database;
      final id = await db.insert(
        'inventory_counts',
        inventoryCount.toMap()..remove('id'), // Remove id for auto-increment
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      return id;
    } catch (e) {
      throw Exception('Failed to create inventory count: $e');
    }
  }

  /// Get all inventory counts
  Future<List<InventoryCountModel>> getAllInventoryCounts() async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'inventory_counts',
        orderBy: 'countDate DESC, id DESC', // Most recent first
      );

      return List.generate(maps.length, (i) {
        return InventoryCountModel.fromMap(maps[i]);
      });
    } catch (e) {
      throw Exception('Failed to get inventory counts: $e');
    }
  }

  /// Get inventory count by ID
  Future<InventoryCountModel?> getInventoryCountById(int id) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'inventory_counts',
        where: 'id = ?',
        whereArgs: [id],
        limit: 1,
      );

      if (maps.isNotEmpty) {
        return InventoryCountModel.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get inventory count by id: $e');
    }
  }

  /// Get inventory counts by product
  Future<List<InventoryCountModel>> getInventoryCountsByProduct(int productId) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'inventory_counts',
        where: 'productId = ?',
        whereArgs: [productId],
        orderBy: 'countDate DESC, id DESC',
      );

      return List.generate(maps.length, (i) {
        return InventoryCountModel.fromMap(maps[i]);
      });
    } catch (e) {
      throw Exception('Failed to get inventory counts by product: $e');
    }
  }

  /// Get inventory counts by date range
  Future<List<InventoryCountModel>> getInventoryCountsByDateRange(
    DateTime fromDate,
    DateTime toDate,
  ) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'inventory_counts',
        where: 'countDate >= ? AND countDate <= ?',
        whereArgs: [fromDate.toIso8601String(), toDate.toIso8601String()],
        orderBy: 'countDate DESC, id DESC',
      );

      return List.generate(maps.length, (i) {
        return InventoryCountModel.fromMap(maps[i]);
      });
    } catch (e) {
      throw Exception('Failed to get inventory counts by date range: $e');
    }
  }

  /// Update inventory count
  Future<void> updateInventoryCount(InventoryCountModel inventoryCount) async {
    try {
      final db = await _databaseService.database;
      await db.update(
        'inventory_counts',
        inventoryCount.toMap(),
        where: 'id = ?',
        whereArgs: [inventoryCount.id],
      );
    } catch (e) {
      throw Exception('Failed to update inventory count: $e');
    }
  }

  /// Delete inventory count
  Future<void> deleteInventoryCount(int id) async {
    try {
      final db = await _databaseService.database;
      await db.delete(
        'inventory_counts',
        where: 'id = ?',
        whereArgs: [id],
      );
    } catch (e) {
      throw Exception('Failed to delete inventory count: $e');
    }
  }

  /// Get inventory count statistics
  Future<Map<String, dynamic>> getInventoryCountStatistics() async {
    try {
      final db = await _databaseService.database;
      
      // Get total counts
      final countResult = await db.rawQuery(
        'SELECT COUNT(*) as count FROM inventory_counts'
      );
      final totalCounts = countResult.first['count'] as int;

      // Get counts with differences
      final differencesResult = await db.rawQuery('''
        SELECT COUNT(*) as count 
        FROM inventory_counts 
        WHERE warehouseDifference != 0 OR storeDifference != 0
      ''');
      final countsWithDifferences = differencesResult.first['count'] as int;

      // Get total differences
      final totalDifferencesResult = await db.rawQuery('''
        SELECT 
          SUM(ABS(warehouseDifference)) as totalWarehouseDifferences,
          SUM(ABS(storeDifference)) as totalStoreDifferences
        FROM inventory_counts
      ''');
      
      final totalWarehouseDifferences = (totalDifferencesResult.first['totalWarehouseDifferences'] as num?)?.toInt() ?? 0;
      final totalStoreDifferences = (totalDifferencesResult.first['totalStoreDifferences'] as num?)?.toInt() ?? 0;

      // Get recent counts (last 30 days)
      final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
      final recentCountsResult = await db.rawQuery('''
        SELECT COUNT(*) as count 
        FROM inventory_counts 
        WHERE countDate >= ?
      ''', [thirtyDaysAgo.toIso8601String()]);
      final recentCounts = recentCountsResult.first['count'] as int;

      return {
        'totalCounts': totalCounts,
        'countsWithDifferences': countsWithDifferences,
        'countsWithoutDifferences': totalCounts - countsWithDifferences,
        'totalWarehouseDifferences': totalWarehouseDifferences,
        'totalStoreDifferences': totalStoreDifferences,
        'recentCounts': recentCounts,
        'accuracyPercentage': totalCounts > 0 
            ? ((totalCounts - countsWithDifferences) / totalCounts * 100).round()
            : 100,
      };
    } catch (e) {
      throw Exception('Failed to get inventory count statistics: $e');
    }
  }

  /// Get products that need counting (haven't been counted recently)
  Future<List<Map<String, dynamic>>> getProductsNeedingCount({int daysSinceLastCount = 30}) async {
    try {
      final db = await _databaseService.database;
      final cutoffDate = DateTime.now().subtract(Duration(days: daysSinceLastCount));
      
      final List<Map<String, dynamic>> maps = await db.rawQuery('''
        SELECT p.id, p.name, p.warehouseQuantity, p.storeQuantity,
               MAX(ic.countDate) as lastCountDate
        FROM products p
        LEFT JOIN inventory_counts ic ON p.id = ic.productId
        GROUP BY p.id, p.name, p.warehouseQuantity, p.storeQuantity
        HAVING lastCountDate IS NULL OR lastCountDate < ?
        ORDER BY lastCountDate ASC, p.name ASC
      ''', [cutoffDate.toIso8601String()]);

      return maps;
    } catch (e) {
      throw Exception('Failed to get products needing count: $e');
    }
  }
}
