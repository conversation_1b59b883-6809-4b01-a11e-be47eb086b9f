import '../../domain/entities/purchase.dart';
import '../../domain/entities/purchase_item.dart';
import '../../domain/repositories/purchase_repository.dart';
import '../datasources/purchase_database_service.dart';
import '../models/purchase_model.dart';
import '../models/purchase_item_model.dart';

class PurchaseRepositoryImpl implements PurchaseRepository {
  final PurchaseDatabaseService _databaseService;

  PurchaseRepositoryImpl(this._databaseService);

  @override
  Future<int> createPurchase(Purchase purchase, List<PurchaseItem> items) async {
    try {
      final purchaseModel = PurchaseModel(
        id: purchase.id,
        supplierId: purchase.supplierId,
        purchaseDate: purchase.purchaseDate,
        totalAmount: purchase.totalAmount,
        paidAmount: purchase.paidAmount,
        dueAmount: purchase.dueAmount,
        paymentMethod: purchase.paymentMethod,
        notes: purchase.notes,
        status: purchase.status,
      );

      final itemModels = items.map((item) => PurchaseItemModel(
        id: item.id,
        purchaseId: item.purchaseId,
        productId: item.productId,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
      )).toList();

      return await _databaseService.createPurchase(purchaseModel, itemModels);
    } catch (e) {
      throw Exception('Failed to create purchase: $e');
    }
  }

  @override
  Future<List<Purchase>> getAllPurchases() async {
    try {
      final purchaseModels = await _databaseService.getAllPurchases();
      return purchaseModels.map((model) => Purchase.fromModel(model)).toList();
    } catch (e) {
      throw Exception('Failed to get all purchases: $e');
    }
  }

  @override
  Future<Purchase?> getPurchaseById(int id) async {
    try {
      final purchaseModel = await _databaseService.getPurchaseById(id);
      return purchaseModel != null ? Purchase.fromModel(purchaseModel) : null;
    } catch (e) {
      throw Exception('Failed to get purchase by id: $e');
    }
  }

  @override
  Future<List<PurchaseItem>> getPurchaseItems(int purchaseId) async {
    try {
      final itemModels = await _databaseService.getPurchaseItems(purchaseId);
      return itemModels.map((model) => PurchaseItem.fromModel(model)).toList();
    } catch (e) {
      throw Exception('Failed to get purchase items: $e');
    }
  }

  @override
  Future<void> updatePurchase(Purchase purchase) async {
    try {
      final purchaseModel = PurchaseModel(
        id: purchase.id,
        supplierId: purchase.supplierId,
        purchaseDate: purchase.purchaseDate,
        totalAmount: purchase.totalAmount,
        paidAmount: purchase.paidAmount,
        dueAmount: purchase.dueAmount,
        paymentMethod: purchase.paymentMethod,
        notes: purchase.notes,
        status: purchase.status,
      );

      await _databaseService.updatePurchase(purchaseModel);
    } catch (e) {
      throw Exception('Failed to update purchase: $e');
    }
  }

  @override
  Future<void> updatePurchaseWithItems(Purchase purchase, List<PurchaseItem> items) async {
    try {
      final purchaseModel = PurchaseModel(
        id: purchase.id,
        supplierId: purchase.supplierId,
        purchaseDate: purchase.purchaseDate,
        totalAmount: purchase.totalAmount,
        paidAmount: purchase.paidAmount,
        dueAmount: purchase.dueAmount,
        paymentMethod: purchase.paymentMethod,
        notes: purchase.notes,
        status: purchase.status,
      );

      final itemModels = items.map((item) => PurchaseItemModel(
        id: item.id,
        purchaseId: item.purchaseId,
        productId: item.productId,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
      )).toList();

      await _databaseService.updatePurchaseWithItems(purchaseModel, itemModels);
    } catch (e) {
      throw Exception('Failed to update purchase with items: $e');
    }
  }

  @override
  Future<void> deletePurchase(int id) async {
    try {
      await _databaseService.deletePurchase(id);
    } catch (e) {
      throw Exception('Failed to delete purchase: $e');
    }
  }

  @override
  Future<List<Purchase>> getPurchasesBySupplier(int supplierId) async {
    try {
      final purchaseModels = await _databaseService.getPurchasesBySupplier(supplierId);
      return purchaseModels.map((model) => Purchase.fromModel(model)).toList();
    } catch (e) {
      throw Exception('Failed to get purchases by supplier: $e');
    }
  }

  @override
  Future<List<Purchase>> getPurchasesByStatus(String status) async {
    try {
      final purchaseModels = await _databaseService.getPurchasesByStatus(status);
      return purchaseModels.map((model) => Purchase.fromModel(model)).toList();
    } catch (e) {
      throw Exception('Failed to get purchases by status: $e');
    }
  }

  @override
  Future<List<Purchase>> getPurchasesByPaymentMethod(String paymentMethod) async {
    try {
      final purchaseModels = await _databaseService.getPurchasesByPaymentMethod(paymentMethod);
      return purchaseModels.map((model) => Purchase.fromModel(model)).toList();
    } catch (e) {
      throw Exception('Failed to get purchases by payment method: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> getPurchaseStatistics() async {
    try {
      return await _databaseService.getPurchaseStatistics();
    } catch (e) {
      throw Exception('Failed to get purchase statistics: $e');
    }
  }
}
