class ActivityModel {
  final int? id;
  final String type;
  final DateTime date;
  final String description;
  final int? relatedId;
  final double? amount;
  final bool canEdit;
  final String? targetRoute;
  final bool isModified;

  const ActivityModel({
    this.id,
    required this.type,
    required this.date,
    required this.description,
    this.relatedId,
    this.amount,
    this.canEdit = true,
    this.targetRoute,
    this.isModified = false,
  });

  // Convert from Map (from database)
  factory ActivityModel.fromMap(Map<String, dynamic> map) {
    return ActivityModel(
      id: map['id'] as int?,
      type: map['type'] as String,
      date: DateTime.parse(map['date'] as String),
      description: map['description'] as String,
      relatedId: map['relatedId'] as int?,
      amount: map['amount'] != null ? (map['amount'] as num).toDouble() : null,
      canEdit: (map['canEdit'] as int?) == 1,
      targetRoute: map['targetRoute'] as String?,
      isModified: (map['isModified'] as int?) == 1,
    );
  }

  // Convert to Map (for database)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'type': type,
      'date': date.toIso8601String(),
      'description': description,
      'relatedId': relatedId,
      'amount': amount,
      'canEdit': canEdit ? 1 : 0,
      'targetRoute': targetRoute,
      'isModified': isModified ? 1 : 0,
    };
  }

  // Copy with method for updates
  ActivityModel copyWith({
    int? id,
    String? type,
    DateTime? date,
    String? description,
    int? relatedId,
    double? amount,
    bool? canEdit,
    String? targetRoute,
    bool? isModified,
  }) {
    return ActivityModel(
      id: id ?? this.id,
      type: type ?? this.type,
      date: date ?? this.date,
      description: description ?? this.description,
      relatedId: relatedId ?? this.relatedId,
      amount: amount ?? this.amount,
      canEdit: canEdit ?? this.canEdit,
      targetRoute: targetRoute ?? this.targetRoute,
      isModified: isModified ?? this.isModified,
    );
  }

  @override
  String toString() {
    return 'ActivityModel(id: $id, type: $type, date: $date, '
        'description: $description, relatedId: $relatedId, amount: $amount, '
        'canEdit: $canEdit, targetRoute: $targetRoute, isModified: $isModified)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ActivityModel &&
        other.id == id &&
        other.type == type &&
        other.date == date &&
        other.description == description &&
        other.relatedId == relatedId &&
        other.amount == amount &&
        other.canEdit == canEdit &&
        other.targetRoute == targetRoute &&
        other.isModified == isModified;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        type.hashCode ^
        date.hashCode ^
        description.hashCode ^
        relatedId.hashCode ^
        amount.hashCode ^
        canEdit.hashCode ^
        targetRoute.hashCode ^
        isModified.hashCode;
  }
}
