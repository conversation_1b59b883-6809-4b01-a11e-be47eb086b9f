import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import '../../domain/entities/purchase.dart';
import '../../domain/entities/purchase_item.dart';
import '../../domain/usecases/create_purchase.dart';
import '../../domain/usecases/get_all_purchases.dart';
import '../../domain/usecases/get_purchase_by_id.dart';
import '../../domain/usecases/update_purchase.dart';
import '../../domain/usecases/delete_purchase.dart';
import '../../domain/repositories/purchase_repository.dart';
import '../../../products/presentation/providers/product_provider.dart';
import '../../../products/domain/usecases/add_purchase_batch.dart';
import '../../../products/domain/entities/purchase_batch.dart';

import '../../../activities/presentation/providers/activity_provider.dart';

class PurchaseProvider extends ChangeNotifier {
  final CreatePurchaseUseCase _createPurchaseUseCase;
  final GetAllPurchasesUseCase _getAllPurchasesUseCase;
  final GetPurchaseByIdUseCase _getPurchaseByIdUseCase;
  final UpdatePurchaseUseCase _updatePurchaseUseCase;
  final DeletePurchaseUseCase _deletePurchaseUseCase;
  final PurchaseRepository _purchaseRepository;
  final AddPurchaseBatchUseCase _addPurchaseBatchUseCase;

  PurchaseProvider()
    : _createPurchaseUseCase = GetIt.instance<CreatePurchaseUseCase>(),
      _getAllPurchasesUseCase = GetIt.instance<GetAllPurchasesUseCase>(),
      _getPurchaseByIdUseCase = GetIt.instance<GetPurchaseByIdUseCase>(),
      _updatePurchaseUseCase = GetIt.instance<UpdatePurchaseUseCase>(),
      _deletePurchaseUseCase = GetIt.instance<DeletePurchaseUseCase>(),
      _purchaseRepository = GetIt.instance<PurchaseRepository>(),
      _addPurchaseBatchUseCase = GetIt.instance<AddPurchaseBatchUseCase>();

  // State variables
  List<Purchase> _purchases = [];
  bool _isLoading = false;
  String? _errorMessage;

  // Filter variables
  String? _selectedPaymentStatus;
  String? _selectedPaymentMethod;
  int? _selectedSupplierId;
  DateTime? _selectedFromDate;
  DateTime? _selectedToDate;

  // Getters
  List<Purchase> get purchases => _purchases;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  String? get selectedPaymentStatus => _selectedPaymentStatus;
  String? get selectedPaymentMethod => _selectedPaymentMethod;
  int? get selectedSupplierId => _selectedSupplierId;
  DateTime? get selectedFromDate => _selectedFromDate;
  DateTime? get selectedToDate => _selectedToDate;

  // Fetch all purchases
  Future<void> fetchPurchases() async {
    _setLoading(true);
    _clearError();

    try {
      _purchases = await _getAllPurchasesUseCase.call();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load purchases: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Create purchase with atomic transaction
  Future<bool> createPurchase(
    Purchase purchase,
    List<PurchaseItem> items,
  ) async {
    _setLoading(true);
    _clearError();

    try {
      // Get required providers
      final productProvider = GetIt.instance<ProductProvider>();
      final activityProvider = GetIt.instance<ActivityProvider>();

      // Create purchase using use case (this handles the database transaction)
      await _createPurchaseUseCase.call(purchase, items);

      // Update product quantities, batches, and prices
      for (final item in items) {
        // TODO: Add price change notification when getProductById is fixed
        // Get current product to check for price changes
        // final product = await productProvider.getProductById(item.productId);

        // Add purchase batch
        final batch = PurchaseBatch(
          productId: item.productId,
          purchaseDate: purchase.purchaseDate,
          quantity: item.quantity,
          unitPurchasePrice: item.unitPrice,
          remainingQuantity: item.quantity,
          isActive: true,
        );
        await _addPurchaseBatchUseCase.call(batch);

        // Update product quantities and last purchase price
        await productProvider.updateProductQuantitiesForSalesAndPurchases(
          item.productId,
          item.quantity,
          isDecrease: false, // Increase for purchase
          newPurchasePrice: item.unitPrice,
        );
      }

      // Refresh purchases list
      await fetchPurchases();

      // Refresh activities
      await activityProvider.refreshActivities();

      return true;
    } catch (e) {
      _setError('Failed to create purchase: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Update purchase
  Future<bool> updatePurchase(Purchase purchase) async {
    _setLoading(true);
    _clearError();

    try {
      await _updatePurchaseUseCase.call(purchase);
      await fetchPurchases(); // Refresh list
      return true;
    } catch (e) {
      _setError('Failed to update purchase: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Update purchase with items
  Future<bool> updatePurchaseWithItems(
    Purchase purchase,
    List<PurchaseItem> items,
  ) async {
    _setLoading(true);
    _clearError();

    try {
      await _purchaseRepository.updatePurchaseWithItems(purchase, items);
      await fetchPurchases(); // Refresh list
      return true;
    } catch (e) {
      _setError('Failed to update purchase with items: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Delete purchase
  Future<bool> deletePurchase(int id) async {
    _setLoading(true);
    _clearError();

    try {
      await _deletePurchaseUseCase.call(id);
      await fetchPurchases(); // Refresh list
      return true;
    } catch (e) {
      _setError('Failed to delete purchase: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Get purchase by ID
  Future<Purchase?> getPurchaseById(int id) async {
    try {
      return await _getPurchaseByIdUseCase.call(id);
    } catch (e) {
      _setError('Failed to get purchase: ${e.toString()}');
      return null;
    }
  }

  // Get purchase items
  Future<List<PurchaseItem>> getPurchaseItems(int purchaseId) async {
    try {
      return await _purchaseRepository.getPurchaseItems(purchaseId);
    } catch (e) {
      _setError('Failed to get purchase items: ${e.toString()}');
      return [];
    }
  }

  // Get purchases by supplier
  Future<List<Purchase>> getPurchasesBySupplier(int supplierId) async {
    try {
      return await _purchaseRepository.getPurchasesBySupplier(supplierId);
    } catch (e) {
      _setError('Failed to get purchases by supplier: ${e.toString()}');
      return [];
    }
  }

  // Get purchases by status
  List<Purchase> getPurchasesByStatus(String status) {
    return _purchases.where((purchase) => purchase.status == status).toList();
  }

  // Get purchases by payment method
  List<Purchase> getPurchasesByPaymentMethod(String paymentMethod) {
    return _purchases
        .where((purchase) => purchase.paymentMethod == paymentMethod)
        .toList();
  }

  // Get purchase statistics
  Future<Map<String, dynamic>> getPurchaseStatistics() async {
    try {
      return await _purchaseRepository.getPurchaseStatistics();
    } catch (e) {
      _setError('Failed to get purchase statistics: ${e.toString()}');
      return {};
    }
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
  }

  // Clear purchases list
  void clearPurchases() {
    _purchases.clear();
    notifyListeners();
  }

  // Search purchases
  List<Purchase> searchPurchases(String query) {
    if (query.isEmpty) return _purchases;

    return _purchases.where((purchase) {
      final purchaseId = purchase.id?.toString() ?? '';
      final supplierId = purchase.supplierId?.toString() ?? '';
      final searchQuery = query.toLowerCase();

      return purchaseId.contains(searchQuery) ||
          supplierId.contains(searchQuery);
    }).toList();
  }

  // Filter purchases
  void filterPurchases({
    String? paymentStatus,
    String? paymentMethod,
    int? supplierId,
    DateTime? fromDate,
    DateTime? toDate,
  }) {
    _selectedPaymentStatus = paymentStatus;
    _selectedPaymentMethod = paymentMethod;
    _selectedSupplierId = supplierId;
    _selectedFromDate = fromDate;
    _selectedToDate = toDate;
    notifyListeners();
  }

  // Get filtered purchases (by all filters and search)
  List<Purchase> getFilteredPurchases(String searchQuery) {
    List<Purchase> filteredPurchases = _purchases;

    // Apply payment status filter
    if (_selectedPaymentStatus != null && _selectedPaymentStatus!.isNotEmpty) {
      switch (_selectedPaymentStatus) {
        case 'مدفوعة بالكامل':
          filteredPurchases = filteredPurchases
              .where((purchase) => purchase.paidAmount >= purchase.totalAmount)
              .toList();
          break;
        case 'مدفوعة جزئياً':
          filteredPurchases = filteredPurchases
              .where(
                (purchase) =>
                    purchase.paidAmount > 0 &&
                    purchase.paidAmount < purchase.totalAmount,
              )
              .toList();
          break;
        case 'غير مدفوعة':
          filteredPurchases = filteredPurchases
              .where((purchase) => purchase.paidAmount == 0)
              .toList();
          break;
      }
    }

    // Apply payment method filter
    if (_selectedPaymentMethod != null && _selectedPaymentMethod!.isNotEmpty) {
      filteredPurchases = filteredPurchases
          .where((purchase) => purchase.paymentMethod == _selectedPaymentMethod)
          .toList();
    }

    // Apply supplier filter
    if (_selectedSupplierId != null) {
      filteredPurchases = filteredPurchases
          .where((purchase) => purchase.supplierId == _selectedSupplierId)
          .toList();
    }

    // Apply date range filter
    if (_selectedFromDate != null) {
      filteredPurchases = filteredPurchases
          .where(
            (purchase) =>
                purchase.purchaseDate.isAfter(_selectedFromDate!) ||
                purchase.purchaseDate.isAtSameMomentAs(_selectedFromDate!),
          )
          .toList();
    }
    if (_selectedToDate != null) {
      final endOfDay = DateTime(
        _selectedToDate!.year,
        _selectedToDate!.month,
        _selectedToDate!.day,
        23,
        59,
        59,
      );
      filteredPurchases = filteredPurchases
          .where(
            (purchase) =>
                purchase.purchaseDate.isBefore(endOfDay) ||
                purchase.purchaseDate.isAtSameMomentAs(endOfDay),
          )
          .toList();
    }

    // Apply search filter
    if (searchQuery.isNotEmpty) {
      filteredPurchases = filteredPurchases.where((purchase) {
        final purchaseId = purchase.id?.toString() ?? '';
        final supplierId = purchase.supplierId?.toString() ?? '';
        final searchQueryLower = searchQuery.toLowerCase();

        return purchaseId.contains(searchQueryLower) ||
            supplierId.contains(searchQueryLower);
      }).toList();
    }

    return filteredPurchases;
  }

  // Clear all filters
  void clearFilters() {
    _selectedPaymentStatus = null;
    _selectedPaymentMethod = null;
    _selectedSupplierId = null;
    _selectedFromDate = null;
    _selectedToDate = null;
    notifyListeners();
  }
}
