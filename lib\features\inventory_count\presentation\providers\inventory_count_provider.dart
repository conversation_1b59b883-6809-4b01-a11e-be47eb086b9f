import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import '../../domain/entities/inventory_count.dart';
import '../../domain/usecases/create_inventory_count.dart';
import '../../domain/usecases/get_all_inventory_counts.dart';
import '../../domain/usecases/get_inventory_count_by_id.dart';
import '../../domain/repositories/inventory_count_repository.dart';
import '../../../products/domain/entities/product.dart';
import '../../../products/domain/usecases/get_all_products.dart';
import '../../../products/domain/usecases/update_product.dart';
import '../../../activities/presentation/providers/activity_provider.dart';

class InventoryCountProvider extends ChangeNotifier {
  final CreateInventoryCountUseCase _createInventoryCountUseCase;
  final GetAllInventoryCountsUseCase _getAllInventoryCountsUseCase;
  final GetInventoryCountByIdUseCase _getInventoryCountByIdUseCase;
  final InventoryCountRepository _inventoryCountRepository;

  InventoryCountProvider()
    : _createInventoryCountUseCase =
          GetIt.instance<CreateInventoryCountUseCase>(),
      _getAllInventoryCountsUseCase =
          GetIt.instance<GetAllInventoryCountsUseCase>(),
      _getInventoryCountByIdUseCase =
          GetIt.instance<GetInventoryCountByIdUseCase>(),
      _inventoryCountRepository = GetIt.instance<InventoryCountRepository>();

  // State variables
  List<InventoryCount> _inventoryCounts = [];
  List<Product> _productsForCount = [];
  final Map<int, int> _countedWarehouseQuantities = {};
  final Map<int, int> _countedStoreQuantities = {};
  bool _isLoading = false;
  String? _errorMessage;

  // Getters
  List<InventoryCount> get inventoryCounts => _inventoryCounts;
  List<Product> get productsForCount => _productsForCount;
  Map<int, int> get countedWarehouseQuantities => _countedWarehouseQuantities;
  Map<int, int> get countedStoreQuantities => _countedStoreQuantities;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  // Fetch all inventory counts
  Future<void> fetchInventoryCounts() async {
    _setLoading(true);
    _clearError();

    try {
      _inventoryCounts = await _getAllInventoryCountsUseCase.call();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load inventory counts: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Load products for counting
  Future<void> loadProductsForCount() async {
    _setLoading(true);
    _clearError();

    try {
      // Get products directly from use case instead of ProductProvider
      final getAllProductsUseCase = GetIt.instance<GetAllProductsUseCase>();
      _productsForCount = await getAllProductsUseCase.call();

      // Initialize counted quantities with current system quantities
      _countedWarehouseQuantities.clear();
      _countedStoreQuantities.clear();

      for (final product in _productsForCount) {
        _countedWarehouseQuantities[product.id!] = product.warehouseQuantity;
        _countedStoreQuantities[product.id!] = product.storeQuantity;
      }

      notifyListeners();
    } catch (e) {
      _setError('Failed to load products for count: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Update counted warehouse quantity for a product
  void updateCountedWarehouseQuantity(int productId, int quantity) {
    if (quantity < 0) return;
    _countedWarehouseQuantities[productId] = quantity;
    notifyListeners();
  }

  // Update counted store quantity for a product
  void updateCountedStoreQuantity(int productId, int quantity) {
    if (quantity < 0) return;
    _countedStoreQuantities[productId] = quantity;
    notifyListeners();
  }

  // Get warehouse difference for a product
  int getWarehouseDifference(int productId) {
    final product = _productsForCount.firstWhere((p) => p.id == productId);
    final countedQuantity =
        _countedWarehouseQuantities[productId] ?? product.warehouseQuantity;
    return countedQuantity - product.warehouseQuantity;
  }

  // Get store difference for a product
  int getStoreDifference(int productId) {
    final product = _productsForCount.firstWhere((p) => p.id == productId);
    final countedQuantity =
        _countedStoreQuantities[productId] ?? product.storeQuantity;
    return countedQuantity - product.storeQuantity;
  }

  // Check if product has any differences
  bool hasAnyDifference(int productId) {
    return getWarehouseDifference(productId) != 0 ||
        getStoreDifference(productId) != 0;
  }

  // Get products with differences
  List<Product> getProductsWithDifferences() {
    return _productsForCount
        .where((product) => hasAnyDifference(product.id!))
        .toList();
  }

  // Create inventory count and reconcile quantities
  Future<bool> createInventoryCount({String? notes}) async {
    _setLoading(true);
    _clearError();

    try {
      final updateProductUseCase = GetIt.instance<UpdateProductUseCase>();
      final activityProvider = GetIt.instance<ActivityProvider>();

      final List<InventoryCount> countsToCreate = [];
      final List<Product> productsToUpdate = [];

      // Create inventory count records for all products
      for (final product in _productsForCount) {
        final countedWarehouseQty =
            _countedWarehouseQuantities[product.id!] ??
            product.warehouseQuantity;
        final countedStoreQty =
            _countedStoreQuantities[product.id!] ?? product.storeQuantity;

        final inventoryCount = InventoryCount.fromQuantities(
          productId: product.id!,
          countDate: DateTime.now(),
          countedWarehouseQuantity: countedWarehouseQty,
          countedStoreQuantity: countedStoreQty,
          systemWarehouseQuantity: product.warehouseQuantity,
          systemStoreQuantity: product.storeQuantity,
          notes: notes,
        );

        countsToCreate.add(inventoryCount);

        // If there are differences, prepare product for update
        if (inventoryCount.hasAnyDifference) {
          final updatedProduct = product.copyWith(
            warehouseQuantity: countedWarehouseQty,
            storeQuantity: countedStoreQty,
          );
          productsToUpdate.add(updatedProduct);
        }
      }

      // Save inventory counts
      for (final count in countsToCreate) {
        await _createInventoryCountUseCase.call(count);
      }

      // Update product quantities if there are differences
      for (final product in productsToUpdate) {
        await updateProductUseCase.call(product);
      }

      // Refresh data
      await fetchInventoryCounts();
      await loadProductsForCount();
      await activityProvider.refreshActivities();

      // Clear counted quantities
      _countedWarehouseQuantities.clear();
      _countedStoreQuantities.clear();
      _productsForCount.clear();

      return true;
    } catch (e) {
      _setError('Failed to create inventory count: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Get inventory count by ID
  Future<InventoryCount?> getInventoryCountById(int id) async {
    try {
      return await _getInventoryCountByIdUseCase.call(id);
    } catch (e) {
      _setError('Failed to get inventory count: ${e.toString()}');
      return null;
    }
  }

  // Get inventory count statistics
  Future<Map<String, dynamic>> getInventoryCountStatistics() async {
    try {
      return await _inventoryCountRepository.getInventoryCountStatistics();
    } catch (e) {
      _setError('Failed to get inventory count statistics: ${e.toString()}');
      return {};
    }
  }

  // Get products needing count
  Future<List<Map<String, dynamic>>> getProductsNeedingCount({
    int daysSinceLastCount = 30,
  }) async {
    try {
      return await _inventoryCountRepository.getProductsNeedingCount(
        daysSinceLastCount: daysSinceLastCount,
      );
    } catch (e) {
      _setError('Failed to get products needing count: ${e.toString()}');
      return [];
    }
  }

  // Reset counted quantities to system quantities
  void resetCountedQuantities() {
    for (final product in _productsForCount) {
      _countedWarehouseQuantities[product.id!] = product.warehouseQuantity;
      _countedStoreQuantities[product.id!] = product.storeQuantity;
    }
    notifyListeners();
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
  }

  // Delete inventory count
  Future<void> deleteInventoryCount(int inventoryCountId) async {
    try {
      // For now, we'll implement a simple delete by removing from the list
      // In a real implementation, you would call a DeleteInventoryCount use case
      _inventoryCounts.removeWhere((count) => count.id == inventoryCountId);
      notifyListeners();
    } catch (e) {
      _setError('فشل في حذف الجرد: ${e.toString()}');
      rethrow;
    }
  }

  // Clear inventory counts list
  void clearInventoryCounts() {
    _inventoryCounts.clear();
    _productsForCount.clear();
    _countedWarehouseQuantities.clear();
    _countedStoreQuantities.clear();
    notifyListeners();
  }
}
