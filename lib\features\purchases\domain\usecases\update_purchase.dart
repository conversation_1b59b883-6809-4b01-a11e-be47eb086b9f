import '../entities/purchase.dart';
import '../repositories/purchase_repository.dart';

class UpdatePurchaseUseCase {
  final PurchaseRepository _repository;

  UpdatePurchaseUseCase(this._repository);

  Future<void> call(Purchase purchase) async {
    // Validation
    if (purchase.id == null || purchase.id! <= 0) {
      throw Exception('Purchase ID must be provided and greater than 0');
    }

    if (purchase.totalAmount < 0) {
      throw Exception('Total amount cannot be negative');
    }

    if (purchase.paidAmount < 0) {
      throw Exception('Paid amount cannot be negative');
    }

    if (purchase.dueAmount < 0) {
      throw Exception('Due amount cannot be negative');
    }

    if (purchase.paymentMethod.isEmpty) {
      throw Exception('Payment method cannot be empty');
    }

    if (purchase.status.isEmpty) {
      throw Exception('Purchase status cannot be empty');
    }

    // Validate payment amounts
    if ((purchase.paidAmount + purchase.dueAmount - purchase.totalAmount).abs() > 0.01) {
      throw Exception('Paid amount + due amount must equal total amount');
    }

    try {
      await _repository.updatePurchase(purchase);
    } catch (e) {
      throw Exception('Failed to update purchase: $e');
    }
  }
}
