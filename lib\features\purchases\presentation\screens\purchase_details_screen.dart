import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:market/shared_widgets/wrappers.dart';
import '../providers/purchase_provider.dart';
import '../../domain/entities/purchase.dart';
import 'package:intl/intl.dart';

class PurchaseDetailsScreen extends StatefulWidget {
  final int purchaseId;

  const PurchaseDetailsScreen({super.key, required this.purchaseId});

  @override
  State<PurchaseDetailsScreen> createState() => _PurchaseDetailsScreenState();
}

class _PurchaseDetailsScreenState extends State<PurchaseDetailsScreen> {
  Purchase? purchase;
  bool isLoading = true;
  String? errorMessage;

  @override
  void initState() {
    super.initState();
    _loadPurchase();
  }

  Future<void> _loadPurchase() async {
    try {
      setState(() {
        isLoading = true;
        errorMessage = null;
      });

      final purchaseProvider = context.read<PurchaseProvider>();
      final loadedPurchase = await purchaseProvider.getPurchaseById(
        widget.purchaseId,
      );

      if (mounted) {
        setState(() {
          purchase = loadedPurchase;
          isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          errorMessage = 'خطأ في تحميل بيانات الفاتورة: ${e.toString()}';
          isLoading = false;
        });
      }
    }
  }

  // دوال وهمية للطباعة والمشاركة
  void _printInvoice() {
    // TODO: تنفيذ وظيفة الطباعة الفعلية
    debugPrint('طباعة فاتورة رقم ${widget.purchaseId}');
  }

  void _shareInvoice() {
    // TODO: تنفيذ وظيفة المشاركة الفعلية
    debugPrint('مشاركة فاتورة رقم ${widget.purchaseId}');
  }

  Future<void> _deletePurchase() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text(
          'هل أنت متأكد من حذف هذه الفاتورة؟ لا يمكن التراجع عن هذا الإجراء.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      try {
        await context.read<PurchaseProvider>().deletePurchase(
          widget.purchaseId,
        );
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم حذف الفاتورة بنجاح')),
          );
          context.pop();
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('خطأ في حذف الفاتورة: ${e.toString()}')),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return SecondaryScreenWrapper(
      title: purchase != null
          ? 'فاتورة رقم ${purchase!.id}'
          : 'تفاصيل الفاتورة',
      actions: [
        if (purchase != null) ...[
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4),
            child: IconButton(
              icon: const Icon(Icons.print),
              onPressed: _printInvoice,
              tooltip: 'طباعة',
              style: IconButton.styleFrom(
                backgroundColor: Colors.blue.shade50,
                foregroundColor: Colors.blue.shade700,
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4),
            child: IconButton(
              icon: const Icon(Icons.share),
              onPressed: _shareInvoice,
              tooltip: 'مشاركة',
              style: IconButton.styleFrom(
                backgroundColor: Colors.green.shade50,
                foregroundColor: Colors.green.shade700,
              ),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () => context.go('/purchases/edit/${widget.purchaseId}'),
            tooltip: 'تعديل',
          ),
          IconButton(
            icon: const Icon(Icons.delete),
            onPressed: _deletePurchase,
            tooltip: 'حذف',
          ),
        ],
      ],
      child: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'خطأ في تحميل البيانات',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              errorMessage!,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadPurchase,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (purchase == null) {
      return const Center(child: Text('لم يتم العثور على الفاتورة'));
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildPurchaseHeaderCard(),
          const SizedBox(height: 16),
          _buildSupplierInfoCard(),
          const SizedBox(height: 16),
          _buildPaymentInfoCard(),
          const SizedBox(height: 16),
          _buildStatusInfoCard(),
          const SizedBox(height: 16),
          _buildAdditionalInfoCard(),
        ],
      ),
    );
  }

  Widget _buildPurchaseHeaderCard() {
    final dateFormat = DateFormat('dd/MM/yyyy HH:mm');

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // ترويسة الفاتورة
            Center(
              child: Column(
                children: [
                  Text(
                    'أُسامة ماركت',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.blue.shade800,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'محطة المسعودي - الشارع العام',
                    style: Theme.of(
                      context,
                    ).textTheme.bodySmall?.copyWith(color: Colors.grey[700]),
                  ),
                  Text(
                    'رقم الهاتف: 739740717',
                    style: Theme.of(
                      context,
                    ).textTheme.bodySmall?.copyWith(color: Colors.grey[700]),
                  ),
                ],
              ),
            ),
            const Divider(height: 20, thickness: 1),

            // عنوان الفاتورة ورقمها
            Center(
              child: Text(
                'فاتورة مشتريات رقم ${purchase!.id}',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.orange,
                ),
              ),
            ),
            const SizedBox(height: 16),

            // معلومات الفاتورة
            Row(
              children: [
                const CircleAvatar(
                  radius: 30,
                  backgroundColor: Colors.orange,
                  child: Icon(
                    Icons.shopping_bag,
                    color: Colors.white,
                    size: 30,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'التاريخ: ${dateFormat.format(purchase!.purchaseDate)}',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'المورد: مورد رقم ${purchase!.supplierId}',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '${purchase!.totalAmount.toStringAsFixed(2)} ر.س',
                      style: Theme.of(context).textTheme.headlineSmall
                          ?.copyWith(
                            color: Colors.orange,
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSupplierInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات المورد',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            _buildInfoRow(
              Icons.local_shipping,
              'المورد',
              purchase!.supplierId != null
                  ? 'مورد رقم ${purchase!.supplierId}'
                  : 'مورد غير محدد',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات الدفع',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            _buildInfoRow(
              Icons.payment,
              'طريقة الدفع',
              purchase!.paymentMethodDisplayName,
            ),
            _buildInfoRow(
              Icons.account_balance_wallet,
              'حالة الدفع',
              purchase!.paymentStatusText,
              valueColor: purchase!.paymentStatus == 'paid'
                  ? Colors.green
                  : Colors.orange,
            ),
            _buildInfoRow(
              Icons.money,
              'المبلغ الإجمالي',
              '${purchase!.totalAmount.toStringAsFixed(2)} ر.س',
              valueColor: Colors.orange,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'حالة الفاتورة',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            _buildInfoRow(
              Icons.info,
              'الحالة',
              purchase!.statusDisplayName,
              valueColor: purchase!.status == 'completed'
                  ? Colors.green
                  : Colors.orange,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdditionalInfoCard() {
    final dateFormat = DateFormat('dd/MM/yyyy HH:mm');

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات إضافية',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            if (purchase!.createdAt != null)
              _buildInfoRow(
                Icons.calendar_today,
                'تاريخ الإنشاء',
                dateFormat.format(purchase!.createdAt!),
              ),
            if (purchase!.updatedAt != null)
              _buildInfoRow(
                Icons.update,
                'آخر تحديث',
                dateFormat.format(purchase!.updatedAt!),
              ),
            if (purchase!.notes != null && purchase!.notes!.isNotEmpty)
              _buildInfoRow(Icons.note, 'ملاحظات', purchase!.notes!),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(
    IconData icon,
    String label,
    String value, {
    Color? valueColor,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: Theme.of(
                    context,
                  ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: valueColor,
                    fontWeight: valueColor != null ? FontWeight.bold : null,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
