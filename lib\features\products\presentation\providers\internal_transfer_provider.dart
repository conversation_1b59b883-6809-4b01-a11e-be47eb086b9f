import 'package:flutter/material.dart';
import '../../domain/entities/internal_transfer.dart';
import '../../domain/usecases/create_internal_transfer.dart';
import '../../domain/usecases/get_all_internal_transfers.dart';
import '../../domain/usecases/get_product_by_id.dart';
import 'product_provider.dart';

class InternalTransferProvider extends ChangeNotifier {
  final CreateInternalTransferUseCase _createInternalTransferUseCase;
  final GetAllInternalTransfersUseCase _getAllInternalTransfersUseCase;
  final GetProductByIdUseCase _getProductByIdUseCase;
  final ProductProvider _productProvider;

  InternalTransferProvider(
    this._createInternalTransferUseCase,
    this._getAllInternalTransfersUseCase,
    this._getProductByIdUseCase,
    this._productProvider,
  );

  // State variables
  List<InternalTransfer> _transfers = [];
  bool _isLoading = false;
  String? _errorMessage;

  // Getters
  List<InternalTransfer> get transfers => _transfers;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  // Fetch all internal transfers
  Future<void> fetchTransfers() async {
    _setLoading(true);
    _clearError();

    try {
      _transfers = await _getAllInternalTransfersUseCase.call();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load transfers: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  /// Add internal transfer with FIFO cost calculation and inventory updates
  Future<bool> addInternalTransfer(int productId, int quantity) async {
    _setLoading(true);
    _clearError();

    try {
      // Get current product
      final product = await _getProductByIdUseCase.call(productId);
      if (product == null) {
        throw Exception('Product not found with id: $productId');
      }

      // Check if there's sufficient quantity in warehouse
      if (product.warehouseQuantity < quantity) {
        throw Exception(
          'Insufficient quantity in warehouse. Available: ${product.warehouseQuantity}, Required: $quantity',
        );
      }

      // Calculate cost using FIFO with proper error handling
      double costAtTransfer;
      try {
        costAtTransfer = await _productProvider.getCostForQuantity(
          productId,
          quantity,
        );
      } catch (e) {
        // Check if the error is related to no purchase batches
        String errorMessage = e.toString();
        if (errorMessage.contains('No purchase batches available') ||
            errorMessage.contains('purchase batches') ||
            errorMessage.contains('no purchase batches') ||
            errorMessage.toLowerCase().contains('batch')) {
          _setError(
            'عذراً، لا توجد دفعات شراء متاحة لهذا المنتج. يرجى إضافة دفعة شراء لهذا المنتج أولاً.',
          );
          return false;
        }
        // For other errors, set a generic error message
        _setError('فشل في حساب تكلفة المنتج: $errorMessage');
        return false;
      }

      final costPerUnit = quantity > 0 ? costAtTransfer / quantity : 0.0;

      // Calculate total value using retail price
      final retailPriceAtTransfer = product.retailPrice;
      final totalValue = quantity * retailPriceAtTransfer;

      // Create transfer entity
      final transfer = InternalTransfer(
        productId: productId,
        transferDate: DateTime.now(),
        quantity: quantity,
        retailPriceAtTransfer: retailPriceAtTransfer,
        costAtTransfer: costPerUnit,
        totalValue: totalValue,
      );

      // Add transfer to database
      await _createInternalTransferUseCase.call(transfer);

      // Update product quantities and apply FIFO deduction
      await _productProvider.updateProductQuantitiesAndBatches(
        productId,
        warehouseDecrement: quantity, // Remove from warehouse
        storeIncrement: quantity, // Add to store
      );

      // Refresh transfers list
      await fetchTransfers();

      return true;
    } catch (e) {
      _setError('Failed to add internal transfer: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Get transfers for a specific product
  List<InternalTransfer> getTransfersForProduct(int productId) {
    return _transfers
        .where((transfer) => transfer.productId == productId)
        .toList();
  }

  // Get transfer statistics
  Map<String, dynamic> getTransferStatistics() {
    if (_transfers.isEmpty) {
      return {
        'totalCount': 0,
        'totalValue': 0.0,
        'totalCost': 0.0,
        'totalQuantity': 0,
        'totalProfit': 0.0,
      };
    }

    final totalCount = _transfers.length;
    final totalValue = _transfers.fold<double>(
      0.0,
      (sum, transfer) => sum + transfer.totalValue,
    );
    final totalCost = _transfers.fold<double>(
      0.0,
      (sum, transfer) => sum + (transfer.costAtTransfer * transfer.quantity),
    );
    final totalQuantity = _transfers.fold<int>(
      0,
      (sum, transfer) => sum + transfer.quantity,
    );
    final totalProfit = totalValue - totalCost;

    return {
      'totalCount': totalCount,
      'totalValue': totalValue,
      'totalCost': totalCost,
      'totalQuantity': totalQuantity,
      'totalProfit': totalProfit,
    };
  }

  // Get transfers by date range
  List<InternalTransfer> getTransfersByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) {
    return _transfers.where((transfer) {
      return transfer.transferDate.isAfter(
            startDate.subtract(const Duration(days: 1)),
          ) &&
          transfer.transferDate.isBefore(endDate.add(const Duration(days: 1)));
    }).toList();
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
  }

  // Clear transfers list
  void clearTransfers() {
    _transfers.clear();
    notifyListeners();
  }
}
