import '../../../../core/database/database_service.dart';

class AnalyticsDatabaseService {
  final DatabaseService _databaseService;

  AnalyticsDatabaseService(this._databaseService);

  /// Get sales summary analytics
  Future<Map<String, dynamic>> getSalesSummary({
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      final db = await _databaseService.database;

      String whereClause = '';
      List<dynamic> whereArgs = [];

      if (fromDate != null && toDate != null) {
        whereClause = 'WHERE saleDate >= ? AND saleDate <= ?';
        whereArgs = [fromDate.toIso8601String(), toDate.toIso8601String()];
      }

      // Get sales totals
      final salesResult = await db.rawQuery('''
        SELECT 
          COUNT(*) as totalSales,
          SUM(totalAmount) as totalRevenue,
          SUM(paidAmount) as totalPaid,
          SUM(dueAmount) as totalDue,
          AVG(totalAmount) as averageSaleAmount
        FROM sales 
        $whereClause
      ''', whereArgs);

      // Get sales by payment method
      final paymentMethodResult = await db.rawQuery('''
        SELECT 
          paymentMethod,
          COUNT(*) as count,
          SUM(totalAmount) as amount
        FROM sales 
        $whereClause
        GROUP BY paymentMethod
      ''', whereArgs);

      // Get sales by status
      final statusResult = await db.rawQuery('''
        SELECT 
          status,
          COUNT(*) as count,
          SUM(totalAmount) as amount
        FROM sales 
        $whereClause
        GROUP BY status
      ''', whereArgs);

      final salesData = salesResult.first;

      return {
        'totalSales': salesData['totalSales'] as int,
        'totalRevenue': (salesData['totalRevenue'] as num?)?.toDouble() ?? 0.0,
        'totalPaid': (salesData['totalPaid'] as num?)?.toDouble() ?? 0.0,
        'totalDue': (salesData['totalDue'] as num?)?.toDouble() ?? 0.0,
        'averageSaleAmount':
            (salesData['averageSaleAmount'] as num?)?.toDouble() ?? 0.0,
        'paymentMethods': paymentMethodResult,
        'statuses': statusResult,
      };
    } catch (e) {
      throw Exception('Failed to get sales summary: $e');
    }
  }

  /// Get purchases summary analytics
  Future<Map<String, dynamic>> getPurchasesSummary({
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      final db = await _databaseService.database;

      String whereClause = '';
      List<dynamic> whereArgs = [];

      if (fromDate != null && toDate != null) {
        whereClause = 'WHERE purchaseDate >= ? AND purchaseDate <= ?';
        whereArgs = [fromDate.toIso8601String(), toDate.toIso8601String()];
      }

      // Get purchases totals
      final purchasesResult = await db.rawQuery('''
        SELECT 
          COUNT(*) as totalPurchases,
          SUM(totalAmount) as totalCost,
          SUM(paidAmount) as totalPaid,
          SUM(dueAmount) as totalDue,
          AVG(totalAmount) as averagePurchaseAmount
        FROM purchases 
        $whereClause
      ''', whereArgs);

      // Get purchases by payment method
      final paymentMethodResult = await db.rawQuery('''
        SELECT 
          paymentMethod,
          COUNT(*) as count,
          SUM(totalAmount) as amount
        FROM purchases 
        $whereClause
        GROUP BY paymentMethod
      ''', whereArgs);

      final purchasesData = purchasesResult.first;

      return {
        'totalPurchases': purchasesData['totalPurchases'] as int,
        'totalCost': (purchasesData['totalCost'] as num?)?.toDouble() ?? 0.0,
        'totalPaid': (purchasesData['totalPaid'] as num?)?.toDouble() ?? 0.0,
        'totalDue': (purchasesData['totalDue'] as num?)?.toDouble() ?? 0.0,
        'averagePurchaseAmount':
            (purchasesData['averagePurchaseAmount'] as num?)?.toDouble() ?? 0.0,
        'paymentMethods': paymentMethodResult,
      };
    } catch (e) {
      throw Exception('Failed to get purchases summary: $e');
    }
  }

  /// Get inventory value analytics
  Future<Map<String, dynamic>> getInventoryValue() async {
    try {
      final db = await _databaseService.database;

      // Get total inventory quantities
      final quantityResult = await db.rawQuery('''
        SELECT 
          SUM(warehouseQuantity) as totalWarehouseQuantity,
          SUM(storeQuantity) as totalStoreQuantity,
          COUNT(*) as totalProducts
        FROM products
      ''');

      // Get inventory value using last purchase prices
      final valueResult = await db.rawQuery('''
        SELECT 
          SUM((warehouseQuantity + storeQuantity) * COALESCE(lastPurchasePrice, 0)) as totalInventoryValue,
          SUM((warehouseQuantity + storeQuantity) * wholesalePrice) as totalWholesaleValue,
          SUM((warehouseQuantity + storeQuantity) * retailPrice) as totalRetailValue
        FROM products
      ''');

      // Get low stock products
      final lowStockResult = await db.rawQuery('''
        SELECT COUNT(*) as lowStockCount
        FROM products
        WHERE (warehouseQuantity + storeQuantity) <= minStockQuantity
      ''');

      // Get products by category
      final categoryResult = await db.rawQuery('''
        SELECT 
          category,
          COUNT(*) as productCount,
          SUM(warehouseQuantity + storeQuantity) as totalQuantity
        FROM products
        GROUP BY category
        ORDER BY productCount DESC
      ''');

      final quantityData = quantityResult.first;
      final valueData = valueResult.first;
      final lowStockData = lowStockResult.first;

      return {
        'totalWarehouseQuantity':
            quantityData['totalWarehouseQuantity'] as int? ?? 0,
        'totalStoreQuantity': quantityData['totalStoreQuantity'] as int? ?? 0,
        'totalProducts': quantityData['totalProducts'] as int,
        'totalInventoryValue':
            (valueData['totalInventoryValue'] as num?)?.toDouble() ?? 0.0,
        'totalWholesaleValue':
            (valueData['totalWholesaleValue'] as num?)?.toDouble() ?? 0.0,
        'totalRetailValue':
            (valueData['totalRetailValue'] as num?)?.toDouble() ?? 0.0,
        'lowStockCount': lowStockData['lowStockCount'] as int,
        'categoriesBreakdown': categoryResult,
      };
    } catch (e) {
      throw Exception('Failed to get inventory value: $e');
    }
  }

  /// Get profit analysis (simplified calculation)
  Future<Map<String, dynamic>> getProfitAnalysis({
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      final db = await _databaseService.database;

      String whereClause = '';
      List<dynamic> whereArgs = [];

      if (fromDate != null && toDate != null) {
        whereClause = 'WHERE s.saleDate >= ? AND s.saleDate <= ?';
        whereArgs = [fromDate.toIso8601String(), toDate.toIso8601String()];
      }

      // Get sales revenue
      final revenueResult = await db.rawQuery('''
        SELECT SUM(totalAmount) as totalRevenue
        FROM sales s
        $whereClause
      ''', whereArgs);

      // Get purchase costs (simplified - using total purchase amounts in period)
      String purchaseWhereClause = '';
      if (fromDate != null && toDate != null) {
        purchaseWhereClause = 'WHERE purchaseDate >= ? AND purchaseDate <= ?';
      }

      final costResult = await db.rawQuery('''
        SELECT SUM(totalAmount) as totalCost
        FROM purchases
        $purchaseWhereClause
      ''', whereArgs);

      final totalRevenue =
          (revenueResult.first['totalRevenue'] as num?)?.toDouble() ?? 0.0;
      final totalCost =
          (costResult.first['totalCost'] as num?)?.toDouble() ?? 0.0;
      final grossProfit = totalRevenue - totalCost;
      final profitMargin = totalRevenue > 0
          ? (grossProfit / totalRevenue * 100)
          : 0.0;

      return {
        'totalRevenue': totalRevenue,
        'totalCost': totalCost,
        'grossProfit': grossProfit,
        'profitMargin': profitMargin,
      };
    } catch (e) {
      throw Exception('Failed to get profit analysis: $e');
    }
  }

  /// Get customer analytics
  Future<Map<String, dynamic>> getCustomerAnalytics() async {
    try {
      final db = await _databaseService.database;

      // Get customer account statistics
      final customerStatsResult = await db.rawQuery('''
        SELECT 
          COUNT(DISTINCT customerId) as totalCustomers,
          SUM(CASE WHEN type IN ('sale_invoice', 'retail_debt') THEN amount ELSE 0 END) as totalDebits,
          SUM(CASE WHEN type IN ('payment', 'return') THEN amount ELSE 0 END) as totalCredits
        FROM customer_accounts
      ''');

      // Get customers with outstanding balances
      final outstandingResult = await db.rawQuery('''
        SELECT 
          customerId,
          SUM(CASE WHEN type IN ('sale_invoice', 'retail_debt') THEN amount ELSE 0 END) -
          SUM(CASE WHEN type IN ('payment', 'return') THEN amount ELSE 0 END) as balance
        FROM customer_accounts
        GROUP BY customerId
        HAVING balance > 0
      ''');

      final statsData = customerStatsResult.first;
      final totalDebits = (statsData['totalDebits'] as num?)?.toDouble() ?? 0.0;
      final totalCredits =
          (statsData['totalCredits'] as num?)?.toDouble() ?? 0.0;
      final totalOutstanding = outstandingResult.fold<double>(
        0.0,
        (sum, row) => sum + ((row['balance'] as num?)?.toDouble() ?? 0.0),
      );

      return {
        'totalCustomers': statsData['totalCustomers'] as int? ?? 0,
        'totalDebits': totalDebits,
        'totalCredits': totalCredits,
        'totalOutstanding': totalOutstanding,
        'customersWithOutstanding': outstandingResult.length,
      };
    } catch (e) {
      throw Exception('Failed to get customer analytics: $e');
    }
  }

  /// Get daily sales trend
  Future<List<Map<String, dynamic>>> getDailySalesTrend({
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      final db = await _databaseService.database;

      final defaultFromDate =
          fromDate ?? DateTime.now().subtract(const Duration(days: 30));
      final defaultToDate = toDate ?? DateTime.now();

      final result = await db.rawQuery(
        '''
        SELECT 
          DATE(saleDate) as date,
          COUNT(*) as salesCount,
          SUM(totalAmount) as totalAmount
        FROM sales
        WHERE saleDate >= ? AND saleDate <= ?
        GROUP BY DATE(saleDate)
        ORDER BY date ASC
      ''',
        [defaultFromDate.toIso8601String(), defaultToDate.toIso8601String()],
      );

      return result;
    } catch (e) {
      throw Exception('Failed to get daily sales trend: $e');
    }
  }
}
