import 'dart:io';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
// import 'package:workmanager/workmanager.dart'; // Disabled temporarily
import 'package:googleapis/drive/v3.dart' as drive;
import 'package:googleapis_auth/auth_io.dart';
import 'package:market/core/database/database_service.dart';

class BackupService {
  static BackupService? _instance;
  static BackupService get instance => _instance ??= BackupService._();
  BackupService._();

  static const String _backupTaskName = 'automaticBackup';
  static const String _localBackupFolder = 'market_backups';

  // Google Drive API credentials (you'll need to set these up)
  static const String _googleDriveClientId = 'YOUR_CLIENT_ID';
  static const String _googleDriveClientSecret = 'YOUR_CLIENT_SECRET';
  static const List<String> _googleDriveScopes = [
    drive.DriveApi.driveFileScope,
  ];

  /// Perform local backup of the database
  Future<String> performLocalBackup() async {
    try {
      // Request storage permission
      final permission = await Permission.storage.request();
      if (!permission.isGranted) {
        throw Exception('تم رفض إذن الوصول للتخزين');
      }

      // Get database file path
      final db = await DatabaseService.instance.database;
      final dbPath = db.path;
      final dbFile = File(dbPath);

      if (!await dbFile.exists()) {
        throw Exception('ملف قاعدة البيانات غير موجود');
      }

      // Create backup directory
      final backupDir = await _getLocalBackupDirectory();
      await backupDir.create(recursive: true);

      // Create backup file with timestamp
      final timestamp = DateTime.now().toIso8601String().replaceAll(':', '-');
      final backupFileName = 'market_backup_$timestamp.db';
      final backupFile = File(path.join(backupDir.path, backupFileName));

      // Copy database file to backup location
      await dbFile.copy(backupFile.path);

      // Clean old backups (keep only last 10)
      await _cleanOldBackups(backupDir);

      return backupFile.path;
    } catch (e) {
      throw Exception('فشل في إنشاء النسخة الاحتياطية المحلية: $e');
    }
  }

  /// Restore database from local backup
  Future<void> restoreLocalBackup(String backupFilePath) async {
    try {
      final backupFile = File(backupFilePath);
      if (!await backupFile.exists()) {
        throw Exception('ملف النسخة الاحتياطية غير موجود');
      }

      // Get current database path first
      final db = await DatabaseService.instance.database;
      final dbPath = db.path;
      final dbFile = File(dbPath);

      // Close database connection
      await db.close();

      // Create backup of current database before restore
      if (await dbFile.exists()) {
        final currentBackupPath =
            '${dbPath}_before_restore_${DateTime.now().millisecondsSinceEpoch}';
        await dbFile.copy(currentBackupPath);
      }

      // Copy backup file to database location
      await backupFile.copy(dbPath);

      // Reinitialize database
      await DatabaseService.instance.database;
    } catch (e) {
      throw Exception('فشل في استعادة النسخة الاحتياطية المحلية: $e');
    }
  }

  /// Get list of local backup files
  Future<List<FileSystemEntity>> getLocalBackups() async {
    try {
      final backupDir = await _getLocalBackupDirectory();
      if (!await backupDir.exists()) {
        return [];
      }

      final backups = await backupDir
          .list()
          .where((entity) => entity is File && entity.path.endsWith('.db'))
          .toList();

      // Sort by modification date (newest first)
      backups.sort(
        (a, b) => File(
          b.path,
        ).lastModifiedSync().compareTo(File(a.path).lastModifiedSync()),
      );

      return backups;
    } catch (e) {
      print('خطأ في جلب النسخ الاحتياطية المحلية: $e');
      return [];
    }
  }

  /// Perform Google Drive backup
  Future<String> performGoogleDriveBackup() async {
    try {
      // This is a placeholder implementation
      // You'll need to implement Google Drive authentication and upload
      throw UnimplementedError('النسخ الاحتياطي على Google Drive غير مفعل بعد');

      // TODO: Implement Google Drive backup
      // 1. Authenticate with Google Drive
      // 2. Upload database file
      // 3. Return backup ID or path
    } catch (e) {
      throw Exception('فشل في إنشاء النسخة الاحتياطية على Google Drive: $e');
    }
  }

  /// Restore from Google Drive backup
  Future<void> restoreGoogleDriveBackup(String backupId) async {
    try {
      // This is a placeholder implementation
      throw UnimplementedError('الاستعادة من Google Drive غير مفعلة بعد');

      // TODO: Implement Google Drive restore
      // 1. Authenticate with Google Drive
      // 2. Download backup file
      // 3. Restore database
    } catch (e) {
      throw Exception('فشل في استعادة النسخة الاحتياطية من Google Drive: $e');
    }
  }

  /// Setup automatic backup (disabled temporarily)
  Future<void> setupAutomaticBackup({
    bool enabled = true,
    Duration frequency = const Duration(days: 1),
  }) async {
    try {
      // Workmanager functionality disabled temporarily due to compatibility issues
      print('النسخ الاحتياطي التلقائي معطل مؤقتاً');
    } catch (e) {
      print('خطأ في إعداد النسخ الاحتياطي التلقائي: $e');
    }
  }

  /// Initialize Workmanager for background tasks (disabled temporarily)
  static Future<void> initializeWorkmanager() async {
    try {
      // Workmanager initialization disabled temporarily
      print('تهيئة Workmanager معطلة مؤقتاً');
    } catch (e) {
      print('خطأ في تهيئة Workmanager: $e');
    }
  }

  /// Get local backup directory
  Future<Directory> _getLocalBackupDirectory() async {
    final documentsDir = await getApplicationDocumentsDirectory();
    return Directory(path.join(documentsDir.path, _localBackupFolder));
  }

  /// Clean old backup files (keep only last 10)
  Future<void> _cleanOldBackups(Directory backupDir) async {
    try {
      final backups = await backupDir
          .list()
          .where((entity) => entity is File && entity.path.endsWith('.db'))
          .toList();

      if (backups.length <= 10) return;

      // Sort by modification date (oldest first)
      backups.sort(
        (a, b) => File(
          a.path,
        ).lastModifiedSync().compareTo(File(b.path).lastModifiedSync()),
      );

      // Delete oldest backups (keep only last 10)
      for (int i = 0; i < backups.length - 10; i++) {
        await backups[i].delete();
      }
    } catch (e) {
      print('خطأ في تنظيف النسخ الاحتياطية القديمة: $e');
    }
  }
}

/// Background task callback for automatic backup (disabled temporarily)
@pragma('vm:entry-point')
void callbackDispatcher() {
  // Workmanager functionality disabled temporarily
  print('مهمة النسخ الاحتياطي الخلفية معطلة مؤقتاً');
}
