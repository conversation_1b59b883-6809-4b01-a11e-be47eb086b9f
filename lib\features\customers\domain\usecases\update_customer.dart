import '../entities/customer.dart';
import '../repositories/customer_repository.dart';

class UpdateCustomer {
  final CustomerRepository _repository;

  UpdateCustomer(this._repository);

  Future<void> call(Customer customer) async {
    // Validation
    if (customer.id == null) {
      throw Exception('Customer ID cannot be null for update');
    }

    if (customer.name.trim().isEmpty) {
      throw Exception('Customer name cannot be empty');
    }

    if (customer.creditLimit < 0) {
      throw Exception('Credit limit cannot be negative');
    }

    if (customer.currentBalance < 0) {
      throw Exception('Current balance cannot be negative');
    }

    // Validate email format if provided
    if (customer.email != null && customer.email!.isNotEmpty) {
      final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
      if (!emailRegex.hasMatch(customer.email!)) {
        throw Exception('Invalid email format');
      }
    }

    // Validate phone format if provided
    if (customer.phone != null && customer.phone!.isNotEmpty) {
      final phoneRegex = RegExp(r'^[\d\s\-\+\(\)]+$');
      if (!phoneRegex.hasMatch(customer.phone!)) {
        throw Exception('Invalid phone format');
      }
    }

    try {
      // Check if customer exists
      final existingCustomer = await _repository.getCustomerById(customer.id!);
      if (existingCustomer == null) {
        throw Exception('Customer not found');
      }

      await _repository.updateCustomer(customer);
    } catch (e) {
      throw Exception('Failed to update customer: $e');
    }
  }
}
