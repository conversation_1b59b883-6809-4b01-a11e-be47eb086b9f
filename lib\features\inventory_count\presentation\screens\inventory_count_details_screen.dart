import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:market/shared_widgets/wrappers.dart';
import '../providers/inventory_count_provider.dart';
import '../../domain/entities/inventory_count.dart';
import 'package:intl/intl.dart';

class InventoryCountDetailsScreen extends StatefulWidget {
  final int inventoryCountId;

  const InventoryCountDetailsScreen({
    super.key,
    required this.inventoryCountId,
  });

  @override
  State<InventoryCountDetailsScreen> createState() =>
      _InventoryCountDetailsScreenState();
}

class _InventoryCountDetailsScreenState
    extends State<InventoryCountDetailsScreen> {
  InventoryCount? inventoryCount;
  bool isLoading = true;
  String? errorMessage;

  @override
  void initState() {
    super.initState();
    _loadInventoryCount();
  }

  Future<void> _loadInventoryCount() async {
    try {
      setState(() {
        isLoading = true;
        errorMessage = null;
      });

      final inventoryCountProvider = context.read<InventoryCountProvider>();
      final loadedInventoryCount = await inventoryCountProvider
          .getInventoryCountById(widget.inventoryCountId);

      if (mounted) {
        setState(() {
          inventoryCount = loadedInventoryCount;
          isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          errorMessage = 'خطأ في تحميل بيانات الجرد: ${e.toString()}';
          isLoading = false;
        });
      }
    }
  }

  Future<void> _deleteInventoryCount() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text(
          'هل أنت متأكد من حذف هذا الجرد؟ لا يمكن التراجع عن هذا الإجراء.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      try {
        await context.read<InventoryCountProvider>().deleteInventoryCount(
          widget.inventoryCountId,
        );
        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('تم حذف الجرد بنجاح')));
          context.pop();
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('خطأ في حذف الجرد: ${e.toString()}')),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return SecondaryScreenWrapper(
      title: inventoryCount != null
          ? 'جرد رقم ${inventoryCount!.id}'
          : 'تفاصيل الجرد',
      actions: [
        if (inventoryCount != null) ...[
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('سيتم إضافة وظيفة الطباعة قريباً'),
                ),
              );
            },
            tooltip: 'طباعة',
          ),
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('سيتم إضافة وظيفة المشاركة قريباً'),
                ),
              );
            },
            tooltip: 'مشاركة',
          ),
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () =>
                context.go('/inventory_count/edit/${widget.inventoryCountId}'),
            tooltip: 'تعديل',
          ),
          IconButton(
            icon: const Icon(Icons.delete),
            onPressed: _deleteInventoryCount,
            tooltip: 'حذف',
          ),
        ],
      ],
      child: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'خطأ في تحميل البيانات',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              errorMessage!,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadInventoryCount,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (inventoryCount == null) {
      return const Center(child: Text('لم يتم العثور على الجرد'));
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInventoryCountHeaderCard(),
          const SizedBox(height: 16),
          _buildInventoryCountInfoCard(),
          const SizedBox(height: 16),
          _buildStatusInfoCard(),
          const SizedBox(height: 16),
          _buildAdditionalInfoCard(),
        ],
      ),
    );
  }

  Widget _buildInventoryCountHeaderCard() {
    final dateFormat = DateFormat('dd/MM/yyyy HH:mm');

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            CircleAvatar(
              radius: 30,
              backgroundColor: Colors.teal,
              child: const Icon(Icons.inventory, color: Colors.white, size: 30),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'جرد مخزن رقم ${inventoryCount!.id}',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    dateFormat.format(inventoryCount!.countDate),
                    style: Theme.of(
                      context,
                    ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
                  ),
                ],
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: inventoryCount!.status == 'completed'
                    ? Colors.green
                    : Colors.orange,
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                inventoryCount!.statusDisplayName,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInventoryCountInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات الجرد',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            _buildInfoRow(
              Icons.inventory_2,
              'المنتج',
              'منتج رقم ${inventoryCount!.productId}',
            ),
            _buildInfoRow(
              Icons.numbers,
              'الكمية المتوقعة',
              '${inventoryCount!.expectedQuantity}',
            ),
            _buildInfoRow(
              Icons.fact_check,
              'الكمية الفعلية',
              '${inventoryCount!.actualQuantity}',
            ),
            _buildInfoRow(
              Icons.compare_arrows,
              'الفرق',
              '${inventoryCount!.difference}',
              valueColor: inventoryCount!.difference >= 0
                  ? Colors.green
                  : Colors.red,
            ),
            _buildInfoRow(
              Icons.calendar_today,
              'تاريخ الجرد',
              DateFormat('dd/MM/yyyy').format(inventoryCount!.countDate),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'حالة الجرد',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            _buildInfoRow(
              Icons.info,
              'الحالة',
              inventoryCount!.statusDisplayName,
              valueColor: inventoryCount!.status == 'completed'
                  ? Colors.green
                  : Colors.orange,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdditionalInfoCard() {
    final dateFormat = DateFormat('dd/MM/yyyy HH:mm');

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات إضافية',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            if (inventoryCount!.createdAt != null)
              _buildInfoRow(
                Icons.calendar_today,
                'تاريخ الإنشاء',
                dateFormat.format(inventoryCount!.createdAt!),
              ),
            if (inventoryCount!.updatedAt != null)
              _buildInfoRow(
                Icons.update,
                'آخر تحديث',
                dateFormat.format(inventoryCount!.updatedAt!),
              ),
            if (inventoryCount!.notes != null &&
                inventoryCount!.notes!.isNotEmpty)
              _buildInfoRow(Icons.note, 'ملاحظات', inventoryCount!.notes!),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(
    IconData icon,
    String label,
    String value, {
    Color? valueColor,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: Theme.of(
                    context,
                  ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: valueColor,
                    fontWeight: valueColor != null ? FontWeight.bold : null,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
