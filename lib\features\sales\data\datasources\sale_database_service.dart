import 'package:sqflite/sqflite.dart';
import '../../../../core/database/database_service.dart';
import '../models/sale_model.dart';
import '../models/sale_item_model.dart';

class SaleDatabaseService {
  final DatabaseService _databaseService;

  SaleDatabaseService(this._databaseService);

  /// Create a new sale with its items
  Future<int> createSale(SaleModel sale, List<SaleItemModel> items) async {
    try {
      final db = await _databaseService.database;
      
      return await db.transaction((txn) async {
        // Insert sale
        final saleId = await txn.insert(
          'sales',
          sale.toMap()..remove('id'), // Remove id for auto-increment
          conflictAlgorithm: ConflictAlgorithm.replace,
        );

        // Insert sale items
        for (final item in items) {
          await txn.insert(
            'sale_items',
            item.copyWith(saleId: saleId).toMap()..remove('id'),
            conflictAlgorithm: ConflictAlgorithm.replace,
          );
        }

        return saleId;
      });
    } catch (e) {
      throw Exception('Failed to create sale: $e');
    }
  }

  /// Get all sales
  Future<List<SaleModel>> getAllSales() async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'sales',
        orderBy: 'saleDate DESC, id DESC', // Most recent first
      );

      return List.generate(maps.length, (i) {
        return SaleModel.fromMap(maps[i]);
      });
    } catch (e) {
      throw Exception('Failed to get sales: $e');
    }
  }

  /// Get sale by ID
  Future<SaleModel?> getSaleById(int id) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'sales',
        where: 'id = ?',
        whereArgs: [id],
        limit: 1,
      );

      if (maps.isNotEmpty) {
        return SaleModel.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get sale by id: $e');
    }
  }

  /// Get sale items by sale ID
  Future<List<SaleItemModel>> getSaleItems(int saleId) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'sale_items',
        where: 'saleId = ?',
        whereArgs: [saleId],
        orderBy: 'id ASC',
      );

      return List.generate(maps.length, (i) {
        return SaleItemModel.fromMap(maps[i]);
      });
    } catch (e) {
      throw Exception('Failed to get sale items: $e');
    }
  }

  /// Update sale
  Future<void> updateSale(SaleModel sale) async {
    try {
      final db = await _databaseService.database;
      await db.update(
        'sales',
        sale.toMap(),
        where: 'id = ?',
        whereArgs: [sale.id],
      );
    } catch (e) {
      throw Exception('Failed to update sale: $e');
    }
  }

  /// Update sale with items
  Future<void> updateSaleWithItems(
    SaleModel sale,
    List<SaleItemModel> items,
  ) async {
    try {
      final db = await _databaseService.database;
      
      await db.transaction((txn) async {
        // Update sale
        await txn.update(
          'sales',
          sale.toMap(),
          where: 'id = ?',
          whereArgs: [sale.id],
        );

        // Delete existing items
        await txn.delete(
          'sale_items',
          where: 'saleId = ?',
          whereArgs: [sale.id],
        );

        // Insert new items
        for (final item in items) {
          await txn.insert(
            'sale_items',
            item.copyWith(saleId: sale.id!).toMap()..remove('id'),
            conflictAlgorithm: ConflictAlgorithm.replace,
          );
        }
      });
    } catch (e) {
      throw Exception('Failed to update sale with items: $e');
    }
  }

  /// Delete sale
  Future<void> deleteSale(int id) async {
    try {
      final db = await _databaseService.database;
      await db.delete(
        'sales',
        where: 'id = ?',
        whereArgs: [id],
      );
      // Sale items will be deleted automatically due to CASCADE
    } catch (e) {
      throw Exception('Failed to delete sale: $e');
    }
  }

  /// Get sales by customer
  Future<List<SaleModel>> getSalesByCustomer(int customerId) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'sales',
        where: 'customerId = ?',
        whereArgs: [customerId],
        orderBy: 'saleDate DESC, id DESC',
      );

      return List.generate(maps.length, (i) {
        return SaleModel.fromMap(maps[i]);
      });
    } catch (e) {
      throw Exception('Failed to get sales by customer: $e');
    }
  }

  /// Get sales by status
  Future<List<SaleModel>> getSalesByStatus(String status) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'sales',
        where: 'status = ?',
        whereArgs: [status],
        orderBy: 'saleDate DESC, id DESC',
      );

      return List.generate(maps.length, (i) {
        return SaleModel.fromMap(maps[i]);
      });
    } catch (e) {
      throw Exception('Failed to get sales by status: $e');
    }
  }

  /// Get sales by payment method
  Future<List<SaleModel>> getSalesByPaymentMethod(String paymentMethod) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'sales',
        where: 'paymentMethod = ?',
        whereArgs: [paymentMethod],
        orderBy: 'saleDate DESC, id DESC',
      );

      return List.generate(maps.length, (i) {
        return SaleModel.fromMap(maps[i]);
      });
    } catch (e) {
      throw Exception('Failed to get sales by payment method: $e');
    }
  }

  /// Get sales statistics
  Future<Map<String, dynamic>> getSalesStatistics() async {
    try {
      final db = await _databaseService.database;
      
      // Get total sales count
      final countResult = await db.rawQuery(
        'SELECT COUNT(*) as count FROM sales'
      );
      final totalCount = countResult.first['count'] as int;

      // Get total sales amount
      final amountResult = await db.rawQuery(
        'SELECT SUM(totalAmount) as totalAmount, SUM(paidAmount) as totalPaid, SUM(dueAmount) as totalDue FROM sales'
      );
      final totalAmount = (amountResult.first['totalAmount'] as num?)?.toDouble() ?? 0.0;
      final totalPaid = (amountResult.first['totalPaid'] as num?)?.toDouble() ?? 0.0;
      final totalDue = (amountResult.first['totalDue'] as num?)?.toDouble() ?? 0.0;

      // Get counts by status
      final statusResult = await db.rawQuery('''
        SELECT status, COUNT(*) as count 
        FROM sales 
        GROUP BY status
      ''');

      final statusCounts = <String, int>{};
      for (final row in statusResult) {
        statusCounts[row['status'] as String] = row['count'] as int;
      }

      // Get counts by payment method
      final paymentResult = await db.rawQuery('''
        SELECT paymentMethod, COUNT(*) as count 
        FROM sales 
        GROUP BY paymentMethod
      ''');

      final paymentCounts = <String, int>{};
      for (final row in paymentResult) {
        paymentCounts[row['paymentMethod'] as String] = row['count'] as int;
      }

      return {
        'totalCount': totalCount,
        'totalAmount': totalAmount,
        'totalPaid': totalPaid,
        'totalDue': totalDue,
        'statusCounts': statusCounts,
        'paymentCounts': paymentCounts,
      };
    } catch (e) {
      throw Exception('Failed to get sales statistics: $e');
    }
  }
}
