import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class ModernDrawer extends StatelessWidget {
  const ModernDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          DrawerHeader(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.store,
                  size: 48,
                  color: Theme.of(context).colorScheme.onPrimary,
                ),
                const SizedBox(height: 8),
                Text(
                  'تطبيق ماركت',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Theme.of(context).colorScheme.onPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'إدارة شاملة للمتجر',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(
                      context,
                    ).colorScheme.onPrimary.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ),
          _buildDrawerItem(
            context,
            icon: Icons.dashboard,
            title: 'الواجهة الرئيسية',
            route: '/',
          ),
          _buildDrawerItem(
            context,
            icon: Icons.inventory,
            title: 'المنتجات',
            route: '/products',
          ),
          _buildDrawerItem(
            context,
            icon: Icons.people,
            title: 'العملاء',
            route: '/customers',
          ),
          _buildDrawerItem(
            context,
            icon: Icons.business,
            title: 'الموردون',
            route: '/suppliers',
          ),
          _buildDrawerItem(
            context,
            icon: Icons.point_of_sale,
            title: 'المبيعات',
            route: '/sales',
          ),
          _buildDrawerItem(
            context,
            icon: Icons.shopping_cart,
            title: 'المشتريات',
            route: '/purchases',
          ),
          _buildDrawerItem(
            context,
            icon: Icons.transfer_within_a_station,
            title: 'التحويلات الداخلية',
            route: '/transfers',
          ),
          _buildDrawerItem(
            context,
            icon: Icons.add_shopping_cart,
            title: 'الطلبيات',
            route: '/orders',
          ),
          _buildDrawerItem(
            context,
            icon: Icons.receipt_long,
            title: 'المصروفات',
            route: '/expenses',
          ),
          _buildDrawerItem(
            context,
            icon: Icons.analytics,
            title: 'التقارير',
            route: '/reports',
          ),
          const Divider(),
          _buildDrawerItem(
            context,
            icon: Icons.settings,
            title: 'الإعدادات',
            route: '/settings',
          ),
        ],
      ),
    );
  }

  Widget _buildDrawerItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String route,
  }) {
    final currentRoute = GoRouterState.of(context).fullPath;
    final isSelected = currentRoute == route;

    return ListTile(
      leading: Icon(
        icon,
        color: isSelected
            ? Theme.of(context).colorScheme.primary
            : Theme.of(context).colorScheme.onSurface,
      ),
      title: Text(
        title,
        style: TextStyle(
          color: isSelected
              ? Theme.of(context).colorScheme.primary
              : Theme.of(context).colorScheme.onSurface,
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
        ),
      ),
      selected: isSelected,
      selectedTileColor: Theme.of(
        context,
      ).colorScheme.primary.withValues(alpha: 0.1),
      onTap: () {
        Navigator.of(context).pop(); // Close drawer
        context.go(route);
      },
    );
  }
}
