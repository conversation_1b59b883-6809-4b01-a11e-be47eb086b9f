import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import '../../domain/usecases/get_sales_summary.dart';
import '../../domain/usecases/get_profit_summary.dart';
import '../../domain/usecases/get_inventory_value.dart';

import '../../domain/repositories/analytics_repository.dart';

class AnalyticsProvider extends ChangeNotifier {
  final GetSalesSummaryUseCase _getSalesSummaryUseCase;
  final GetProfitSummaryUseCase _getProfitSummaryUseCase;
  final GetInventoryValueUseCase _getInventoryValueUseCase;

  final AnalyticsRepository _analyticsRepository;

  AnalyticsProvider()
    : _getSalesSummaryUseCase = GetIt.instance<GetSalesSummaryUseCase>(),
      _getProfitSummaryUseCase = GetIt.instance<GetProfitSummaryUseCase>(),
      _getInventoryValueUseCase = GetIt.instance<GetInventoryValueUseCase>(),

      _analyticsRepository = GetIt.instance<AnalyticsRepository>();

  // State variables
  Map<String, dynamic> _salesSummary = {};
  Map<String, dynamic> _purchasesSummary = {};
  Map<String, dynamic> _inventoryValue = {};
  Map<String, dynamic> _profitAnalysis = {};
  Map<String, dynamic> _customerAnalytics = {};
  List<Map<String, dynamic>> _dailySalesTrend = [];

  bool _isLoading = false;
  String? _errorMessage;
  DateTime? _selectedFromDate;
  DateTime? _selectedToDate;

  // Getters
  Map<String, dynamic> get salesSummary => _salesSummary;
  Map<String, dynamic> get purchasesSummary => _purchasesSummary;
  Map<String, dynamic> get inventoryValue => _inventoryValue;
  Map<String, dynamic> get profitAnalysis => _profitAnalysis;
  Map<String, dynamic> get customerAnalytics => _customerAnalytics;
  List<Map<String, dynamic>> get dailySalesTrend => _dailySalesTrend;

  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  DateTime? get selectedFromDate => _selectedFromDate;
  DateTime? get selectedToDate => _selectedToDate;

  // Set date range
  void setDateRange(DateTime? fromDate, DateTime? toDate) {
    _selectedFromDate = fromDate;
    _selectedToDate = toDate;
    notifyListeners();
  }

  // Load all analytics data
  Future<void> loadAllAnalytics() async {
    _setLoading(true);
    _clearError();

    try {
      await Future.wait([
        loadSalesSummary(),
        loadPurchasesSummary(),
        loadInventoryValue(),
        loadProfitAnalysis(),
        loadCustomerAnalytics(),
        loadDailySalesTrend(),
      ]);
    } catch (e) {
      _setError('Failed to load analytics: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Load sales summary
  Future<void> loadSalesSummary() async {
    try {
      _salesSummary = await _getSalesSummaryUseCase.call(
        fromDate: _selectedFromDate,
        toDate: _selectedToDate,
      );
      notifyListeners();
    } catch (e) {
      _setError('Failed to load sales summary: ${e.toString()}');
    }
  }

  // Load purchases summary
  Future<void> loadPurchasesSummary() async {
    try {
      _purchasesSummary = await _analyticsRepository.getPurchasesSummary(
        fromDate: _selectedFromDate,
        toDate: _selectedToDate,
      );
      notifyListeners();
    } catch (e) {
      _setError('Failed to load purchases summary: ${e.toString()}');
    }
  }

  // Load inventory value
  Future<void> loadInventoryValue() async {
    try {
      _inventoryValue = await _getInventoryValueUseCase.call();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load inventory value: ${e.toString()}');
    }
  }

  // Load profit analysis
  Future<void> loadProfitAnalysis() async {
    try {
      _profitAnalysis = await _getProfitSummaryUseCase.call(
        fromDate: _selectedFromDate,
        toDate: _selectedToDate,
      );
      notifyListeners();
    } catch (e) {
      _setError('Failed to load profit analysis: ${e.toString()}');
    }
  }

  // Load customer analytics
  Future<void> loadCustomerAnalytics() async {
    try {
      _customerAnalytics = await _analyticsRepository.getCustomerAnalytics();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load customer analytics: ${e.toString()}');
    }
  }

  // Load daily sales trend
  Future<void> loadDailySalesTrend() async {
    try {
      _dailySalesTrend = await _analyticsRepository.getDailySalesTrend(
        fromDate: _selectedFromDate,
        toDate: _selectedToDate,
      );
      notifyListeners();
    } catch (e) {
      _setError('Failed to load daily sales trend: ${e.toString()}');
    }
  }

  // Get KPI values
  double get totalRevenue =>
      (_salesSummary['totalRevenue'] as num?)?.toDouble() ?? 0.0;
  double get totalCost =>
      (_purchasesSummary['totalCost'] as num?)?.toDouble() ?? 0.0;
  double get grossProfit =>
      (_profitAnalysis['grossProfit'] as num?)?.toDouble() ?? 0.0;
  double get profitMargin =>
      (_profitAnalysis['profitMargin'] as num?)?.toDouble() ?? 0.0;
  double get inventoryTotalValue =>
      (_inventoryValue['totalInventoryValue'] as num?)?.toDouble() ?? 0.0;
  int get totalSales => (_salesSummary['totalSales'] as int?) ?? 0;
  int get totalPurchases => (_purchasesSummary['totalPurchases'] as int?) ?? 0;
  int get totalProducts => (_inventoryValue['totalProducts'] as int?) ?? 0;
  int get lowStockCount => (_inventoryValue['lowStockCount'] as int?) ?? 0;
  double get totalOutstanding =>
      (_customerAnalytics['totalOutstanding'] as num?)?.toDouble() ?? 0.0;

  // Get formatted values
  String get formattedRevenue => '${totalRevenue.toStringAsFixed(2)} ر.ي';
  String get formattedCost => '${totalCost.toStringAsFixed(2)} ر.ي';
  String get formattedProfit => '${grossProfit.toStringAsFixed(2)} ر.ي';
  String get formattedProfitMargin => '${profitMargin.toStringAsFixed(1)}%';
  String get formattedInventoryValue =>
      '${inventoryTotalValue.toStringAsFixed(2)} ر.ي';
  String get formattedOutstanding =>
      '${totalOutstanding.toStringAsFixed(2)} ر.ي';

  // Get period description
  String get periodDescription {
    if (_selectedFromDate == null || _selectedToDate == null) {
      return 'جميع الفترات';
    }

    final fromStr =
        '${_selectedFromDate!.day}/${_selectedFromDate!.month}/${_selectedFromDate!.year}';
    final toStr =
        '${_selectedToDate!.day}/${_selectedToDate!.month}/${_selectedToDate!.year}';
    return 'من $fromStr إلى $toStr';
  }

  // Get quick period options
  void setQuickPeriod(String period) {
    final now = DateTime.now();
    switch (period) {
      case 'today':
        setDateRange(
          DateTime(now.year, now.month, now.day),
          DateTime(now.year, now.month, now.day, 23, 59, 59),
        );
        break;
      case 'week':
        final weekStart = now.subtract(Duration(days: now.weekday - 1));
        setDateRange(
          DateTime(weekStart.year, weekStart.month, weekStart.day),
          DateTime(now.year, now.month, now.day, 23, 59, 59),
        );
        break;
      case 'month':
        setDateRange(
          DateTime(now.year, now.month, 1),
          DateTime(now.year, now.month + 1, 0, 23, 59, 59),
        );
        break;
      case 'year':
        setDateRange(
          DateTime(now.year, 1, 1),
          DateTime(now.year, 12, 31, 23, 59, 59),
        );
        break;
      case 'all':
        setDateRange(null, null);
        break;
    }
    loadAllAnalytics();
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
  }

  // Clear all data
  void clearAnalytics() {
    _salesSummary.clear();
    _purchasesSummary.clear();
    _inventoryValue.clear();
    _profitAnalysis.clear();
    _customerAnalytics.clear();
    _dailySalesTrend.clear();
    _selectedFromDate = null;
    _selectedToDate = null;
    notifyListeners();
  }
}
