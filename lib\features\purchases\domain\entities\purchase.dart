import '../../data/models/purchase_model.dart';

class Purchase {
  final int? id;
  final int? supplierId;
  final DateTime purchaseDate;
  final double totalAmount;
  final double paidAmount;
  final double dueAmount;
  final String paymentMethod;
  final String? notes;
  final String status;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const Purchase({
    this.id,
    this.supplierId,
    required this.purchaseDate,
    required this.totalAmount,
    this.paidAmount = 0.0,
    this.dueAmount = 0.0,
    required this.paymentMethod,
    this.notes,
    required this.status,
    this.createdAt,
    this.updatedAt,
  });

  // Create Purchase from PurchaseModel
  factory Purchase.fromModel(PurchaseModel model) {
    return Purchase(
      id: model.id,
      supplierId: model.supplierId,
      purchaseDate: model.purchaseDate,
      totalAmount: model.totalAmount,
      paidAmount: model.paidAmount,
      dueAmount: model.dueAmount,
      paymentMethod: model.paymentMethod,
      notes: model.notes,
      status: model.status,
    );
  }

  // Business logic methods
  bool get isPaid => dueAmount <= 0;
  bool get isPartiallyPaid => paidAmount > 0 && dueAmount > 0;
  bool get isUnpaid => paidAmount <= 0;
  bool get isCash => paymentMethod == 'cash';
  bool get isCredit => paymentMethod == 'credit';
  bool get isBankTransfer => paymentMethod == 'bank_transfer';

  String get paymentMethodDisplayName {
    switch (paymentMethod) {
      case 'cash':
        return 'نقدي';
      case 'credit':
        return 'آجل';
      case 'bank_transfer':
        return 'تحويل بنكي';
      default:
        return paymentMethod;
    }
  }

  String get statusDisplayName {
    switch (status) {
      case 'completed':
        return 'مكتملة';
      case 'pending_payment':
        return 'في انتظار الدفع';
      case 'cancelled':
        return 'ملغية';
      default:
        return status;
    }
  }

  String get paymentStatusText {
    if (isPaid) return 'مدفوعة بالكامل';
    if (isPartiallyPaid) return 'مدفوعة جزئياً';
    return 'غير مدفوعة';
  }

  String get paymentStatus {
    if (isPaid) return 'paid';
    if (isPartiallyPaid) return 'partial';
    return 'unpaid';
  }

  // Copy with method for updates
  Purchase copyWith({
    int? id,
    int? supplierId,
    DateTime? purchaseDate,
    double? totalAmount,
    double? paidAmount,
    double? dueAmount,
    String? paymentMethod,
    String? notes,
    String? status,
  }) {
    return Purchase(
      id: id ?? this.id,
      supplierId: supplierId ?? this.supplierId,
      purchaseDate: purchaseDate ?? this.purchaseDate,
      totalAmount: totalAmount ?? this.totalAmount,
      paidAmount: paidAmount ?? this.paidAmount,
      dueAmount: dueAmount ?? this.dueAmount,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      notes: notes ?? this.notes,
      status: status ?? this.status,
    );
  }

  @override
  String toString() {
    return 'Purchase(id: $id, supplierId: $supplierId, purchaseDate: $purchaseDate, '
        'totalAmount: $totalAmount, paidAmount: $paidAmount, dueAmount: $dueAmount, '
        'paymentMethod: $paymentMethod, notes: $notes, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Purchase &&
        other.id == id &&
        other.supplierId == supplierId &&
        other.purchaseDate == purchaseDate &&
        other.totalAmount == totalAmount &&
        other.paidAmount == paidAmount &&
        other.dueAmount == dueAmount &&
        other.paymentMethod == paymentMethod &&
        other.notes == notes &&
        other.status == status;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        supplierId.hashCode ^
        purchaseDate.hashCode ^
        totalAmount.hashCode ^
        paidAmount.hashCode ^
        dueAmount.hashCode ^
        paymentMethod.hashCode ^
        notes.hashCode ^
        status.hashCode;
  }
}
