import '../entities/sale.dart';
import '../entities/sale_item.dart';

abstract class SaleRepository {
  /// Create a new sale with its items
  Future<int> createSale(Sale sale, List<SaleItem> items);

  /// Get all sales
  Future<List<Sale>> getAllSales();

  /// Get sale by ID
  Future<Sale?> getSaleById(int id);

  /// Get sale items by sale ID
  Future<List<SaleItem>> getSaleItems(int saleId);

  /// Update sale
  Future<void> updateSale(Sale sale);

  /// Update sale with items
  Future<void> updateSaleWithItems(Sale sale, List<SaleItem> items);

  /// Delete sale
  Future<void> deleteSale(int id);

  /// Get sales by customer
  Future<List<Sale>> getSalesByCustomer(int customerId);

  /// Get sales by status
  Future<List<Sale>> getSalesByStatus(String status);

  /// Get sales by payment method
  Future<List<Sale>> getSalesByPaymentMethod(String paymentMethod);

  /// Get sales statistics
  Future<Map<String, dynamic>> getSalesStatistics();
}
