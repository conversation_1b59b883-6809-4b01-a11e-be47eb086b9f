import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../domain/entities/internal_transfer.dart';
import '../../domain/entities/product.dart';
import '../providers/internal_transfer_provider.dart';
import '../providers/product_provider.dart';
import '../../../../shared_widgets/wrappers.dart';

class InternalTransferScreen extends StatefulWidget {
  const InternalTransferScreen({super.key});

  @override
  State<InternalTransferScreen> createState() => _InternalTransferScreenState();
}

class _InternalTransferScreenState extends State<InternalTransferScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<InternalTransferProvider>().fetchTransfers();
    });
  }

  @override
  Widget build(BuildContext context) {
    return SecondaryScreenWrapper(
      title: 'التحويلات الداخلية',
      child: Scaffold(
        appBar: AppBar(
          title: const Text('التحويلات الداخلية'),
          backgroundColor: Theme.of(context).colorScheme.inversePrimary,
          automaticallyImplyLeading:
              false, // Remove default back button since SecondaryScreenWrapper handles it
          actions: [
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () {
                context.read<InternalTransferProvider>().fetchTransfers();
              },
            ),
          ],
        ),
        body: Consumer<InternalTransferProvider>(
          builder: (context, transferProvider, child) {
            if (transferProvider.isLoading) {
              return const Center(child: CircularProgressIndicator());
            }

            if (transferProvider.errorMessage != null) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
                    const SizedBox(height: 16),
                    Text(
                      transferProvider.errorMessage!,
                      style: const TextStyle(fontSize: 16),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        transferProvider.fetchTransfers();
                      },
                      child: const Text('إعادة المحاولة'),
                    ),
                  ],
                ),
              );
            }

            if (transferProvider.transfers.isEmpty) {
              return const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.transfer_within_a_station,
                      size: 64,
                      color: Colors.grey,
                    ),
                    SizedBox(height: 16),
                    Text(
                      'لا توجد تحويلات داخلية',
                      style: TextStyle(fontSize: 18, color: Colors.grey),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'اضغط على زر + لإضافة تحويل جديد',
                      style: TextStyle(fontSize: 14, color: Colors.grey),
                    ),
                  ],
                ),
              );
            }

            return Column(
              children: [
                // Statistics Card
                _buildStatisticsCard(transferProvider),

                // Transfers List
                Expanded(
                  child: ListView.builder(
                    itemCount: transferProvider.transfers.length,
                    itemBuilder: (context, index) {
                      final transfer = transferProvider.transfers[index];
                      return _buildTransferCard(transfer);
                    },
                  ),
                ),
              ],
            );
          },
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: () => _showAddTransferDialog(context),
          child: const Icon(Icons.add),
        ),
      ),
    );
  }

  Widget _buildStatisticsCard(InternalTransferProvider provider) {
    final stats = provider.getTransferStatistics();

    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إحصائيات التحويلات',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'العدد الكلي',
                    '${stats['totalCount']}',
                    Icons.transfer_within_a_station,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'الكمية الكلية',
                    '${stats['totalQuantity']}',
                    Icons.inventory,
                    Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'القيمة الكلية',
                    '${(stats['totalValue'] as double).toStringAsFixed(2)} ر.ي',
                    Icons.attach_money,
                    Colors.orange,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'الربح المتوقع',
                    '${(stats['totalProfit'] as double).toStringAsFixed(2)} ر.ي',
                    Icons.trending_up,
                    Colors.purple,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: const TextStyle(fontSize: 12, color: Colors.grey),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTransferCard(InternalTransfer transfer) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: Colors.blue.withValues(alpha: 0.1),
          child: const Icon(
            Icons.transfer_within_a_station,
            color: Colors.blue,
          ),
        ),
        title: Text('منتج رقم: ${transfer.productId}'),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('الكمية: ${transfer.quantity}'),
            Text(
              'التاريخ: ${DateFormat('yyyy-MM-dd HH:mm').format(transfer.transferDate)}',
            ),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              '${transfer.totalValue.toStringAsFixed(2)} ر.ي',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.green,
              ),
            ),
            Text(
              'ربح: ${transfer.totalProfit.toStringAsFixed(2)} ر.ي',
              style: const TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        onTap: () => _showTransferDetails(transfer),
      ),
    );
  }

  void _showTransferDetails(InternalTransfer transfer) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تفاصيل التحويل'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('رقم المنتج:', '${transfer.productId}'),
            _buildDetailRow('الكمية:', '${transfer.quantity}'),
            _buildDetailRow(
              'سعر التجزئة:',
              '${transfer.retailPriceAtTransfer.toStringAsFixed(2)} ر.ي',
            ),
            _buildDetailRow(
              'التكلفة:',
              '${transfer.costAtTransfer.toStringAsFixed(2)} ر.ي',
            ),
            _buildDetailRow(
              'القيمة الكلية:',
              '${transfer.totalValue.toStringAsFixed(2)} ر.ي',
            ),
            _buildDetailRow(
              'الربح المتوقع:',
              '${transfer.totalProfit.toStringAsFixed(2)} ر.ي',
            ),
            _buildDetailRow(
              'التاريخ:',
              DateFormat('yyyy-MM-dd HH:mm:ss').format(transfer.transferDate),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(fontWeight: FontWeight.bold)),
          Text(value),
        ],
      ),
    );
  }

  void _showAddTransferDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const AddInternalTransferDialog(),
    );
  }
}

class AddInternalTransferDialog extends StatefulWidget {
  const AddInternalTransferDialog({super.key});

  @override
  State<AddInternalTransferDialog> createState() =>
      _AddInternalTransferDialogState();
}

class _AddInternalTransferDialogState extends State<AddInternalTransferDialog> {
  final _formKey = GlobalKey<FormState>();
  final _quantityController = TextEditingController();
  Product? _selectedProduct;
  bool _isLoading = false;

  @override
  void dispose() {
    _quantityController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
          maxWidth: MediaQuery.of(context).size.width * 0.9,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Title
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.transfer_within_a_station,
                    color: Theme.of(context).primaryColor,
                  ),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      'إضافة تحويل داخلي',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
            ),

            // Content
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Form(
                  key: _formKey,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Product Selection
                      Consumer<ProductProvider>(
                        builder: (context, productProvider, child) {
                          return DropdownButtonFormField<Product>(
                            value: _selectedProduct,
                            decoration: const InputDecoration(
                              labelText: 'اختر المنتج',
                              border: OutlineInputBorder(),
                            ),
                            items: productProvider.products
                                .where(
                                  (product) => product.warehouseQuantity > 0,
                                )
                                .map(
                                  (product) => DropdownMenuItem(
                                    value: product,
                                    child: Text(
                                      '${product.name} (مخزن: ${product.warehouseQuantity})',
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                )
                                .toList(),
                            onChanged: (product) {
                              setState(() {
                                _selectedProduct = product;
                              });
                            },
                            validator: (value) {
                              if (value == null) {
                                return 'يرجى اختيار منتج';
                              }
                              return null;
                            },
                          );
                        },
                      ),
                      const SizedBox(height: 16),

                      // Quantity Input
                      TextFormField(
                        controller: _quantityController,
                        decoration: const InputDecoration(
                          labelText: 'الكمية المراد تحويلها',
                          border: OutlineInputBorder(),
                          suffixText: 'وحدة',
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'يرجى إدخال الكمية';
                          }
                          final quantity = int.tryParse(value);
                          if (quantity == null || quantity <= 0) {
                            return 'يرجى إدخال كمية صحيحة';
                          }
                          if (_selectedProduct != null &&
                              quantity > _selectedProduct!.warehouseQuantity) {
                            return 'الكمية أكبر من المتوفر في المخزن (${_selectedProduct!.warehouseQuantity})';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Product Info Display
                      if (_selectedProduct != null) ...[
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'معلومات المنتج:',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.grey[700],
                                ),
                              ),
                              const SizedBox(height: 8),
                              _buildInfoRow(
                                'الكمية في المخزن:',
                                '${_selectedProduct!.warehouseQuantity}',
                              ),
                              _buildInfoRow(
                                'سعر التجزئة:',
                                '${_selectedProduct!.retailPrice.toStringAsFixed(2)} ر.ي',
                              ),
                              if (_quantityController.text.isNotEmpty &&
                                  int.tryParse(_quantityController.text) !=
                                      null)
                                _buildInfoRow(
                                  'القيمة المتوقعة:',
                                  '${(_selectedProduct!.retailPrice * int.parse(_quantityController.text)).toStringAsFixed(2)} ر.ي',
                                ),
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),

            // Actions
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(8),
                  bottomRight: Radius.circular(8),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: _isLoading
                        ? null
                        : () => Navigator.of(context).pop(),
                    child: const Text('إلغاء'),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed: _isLoading ? null : _handleSubmit,
                    child: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Text('تحويل'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(fontSize: 14)),
          Text(
            value,
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  Future<void> _handleSubmit() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final quantity = int.parse(_quantityController.text);
      final success = await context
          .read<InternalTransferProvider>()
          .addInternalTransfer(_selectedProduct!.id!, quantity);

      if (success) {
        if (mounted) {
          Navigator.of(context).pop();
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إجراء التحويل بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        if (mounted) {
          final errorMessage = context
              .read<InternalTransferProvider>()
              .errorMessage;
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(errorMessage ?? 'فشل في إجراء التحويل'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
