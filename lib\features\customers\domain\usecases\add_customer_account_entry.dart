import '../entities/customer_account.dart';
import '../repositories/customer_account_repository.dart';

class AddCustomerAccountEntryUseCase {
  final CustomerAccountRepository _repository;

  AddCustomerAccountEntryUseCase(this._repository);

  Future<int> call(CustomerAccount entry) async {
    // Validation
    if (entry.customerId <= 0) {
      throw Exception('Customer ID must be greater than 0');
    }

    if (entry.type.isEmpty) {
      throw Exception('Transaction type cannot be empty');
    }

    if (entry.amount < 0) {
      throw Exception('Amount cannot be negative');
    }

    // Validate transaction type
    const validTypes = ['sale_invoice', 'retail_debt', 'payment', 'return'];
    if (!validTypes.contains(entry.type)) {
      throw Exception('Invalid transaction type: ${entry.type}');
    }

    try {
      return await _repository.addCustomerAccountEntry(entry);
    } catch (e) {
      throw Exception('Failed to add customer account entry: $e');
    }
  }
}
